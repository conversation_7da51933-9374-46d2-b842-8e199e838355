<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mysql')->create('setting_role', function (Blueprint $table) {
            $table->id();
            $table->string('shortname');
            $table->string('username')->unique();
            $table->integer('crud_invoice')->nullable();
            $table->integer('pay_invoice')->nullable();
            $table->integer('ringkasan_invoice')->nullable();
            $table->integer('menu_keuangan')->nullable();
            $table->integer('ringkasan_keuangan')->nullable();
            $table->integer('crud_transaksi')->nullable();
            $table->integer('laporan_keuangan')->nullable();
            $table->integer('payment_gateway')->nullable();
            $table->integer('menu_radius')->nullable();
            $table->integer('create_user_hs')->nullable();
            $table->integer('create_user_pppoe')->nullable();
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('setting_role');
    }
};

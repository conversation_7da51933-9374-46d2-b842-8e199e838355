<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('frradius_auth')->create('user_profile', function (Blueprint $table) {
            $table->id();
            $table->string('shortname');
            $table->integer('mode');
            $table->string('profile');
            $table->string('attribute');
            $table->string('value');
            $table->string('op')->default(':=');
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_profile');
    }
};

/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */
@layer properties{
    @supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){
        *,:before,:after,::backdrop{
            --tw-border-spacing-x:0;
            --tw-border-spacing-y:0;
            --tw-translate-x:0;
            --tw-translate-y:0;
            --tw-translate-z:0;
            --tw-rotate-x:initial;
            --tw-rotate-y:initial;
            --tw-rotate-z:initial;
            --tw-skew-x:initial;
            --tw-skew-y:initial;
            --tw-space-y-reverse:0;
            --tw-space-x-reverse:0;
            --tw-divide-y-reverse:0;
            --tw-border-style:solid;
            --tw-gradient-position:initial;
            --tw-gradient-from:#0000;
            --tw-gradient-via:#0000;
            --tw-gradient-to:#0000;
            --tw-gradient-stops:initial;
            --tw-gradient-via-stops:initial;
            --tw-gradient-from-position:0%;
            --tw-gradient-via-position:50%;
            --tw-gradient-to-position:100%;
            --tw-leading:initial;
            --tw-font-weight:initial;
            --tw-tracking:initial;
            --tw-shadow:0 0 #0000;
            --tw-shadow-color:initial;
            --tw-shadow-alpha:100%;
            --tw-inset-shadow:0 0 #0000;
            --tw-inset-shadow-color:initial;
            --tw-inset-shadow-alpha:100%;
            --tw-ring-color:initial;
            --tw-ring-shadow:0 0 #0000;
            --tw-inset-ring-color:initial;
            --tw-inset-ring-shadow:0 0 #0000;
            --tw-ring-inset:initial;
            --tw-ring-offset-width:0px;
            --tw-ring-offset-color:#fff;
            --tw-ring-offset-shadow:0 0 #0000;
            --tw-outline-style:solid;
            --tw-blur:initial;
            --tw-brightness:initial;
            --tw-contrast:initial;
            --tw-grayscale:initial;
            --tw-hue-rotate:initial;
            --tw-invert:initial;
            --tw-opacity:initial;
            --tw-saturate:initial;
            --tw-sepia:initial;
            --tw-drop-shadow:initial;
            --tw-drop-shadow-color:initial;
            --tw-drop-shadow-alpha:100%;
            --tw-drop-shadow-size:initial;
            --tw-backdrop-blur:initial;
            --tw-backdrop-brightness:initial;
            --tw-backdrop-contrast:initial;
            --tw-backdrop-grayscale:initial;
            --tw-backdrop-hue-rotate:initial;
            --tw-backdrop-invert:initial;
            --tw-backdrop-opacity:initial;
            --tw-backdrop-saturate:initial;
            --tw-backdrop-sepia:initial;
            --tw-duration:initial;
            --tw-content:"";
            --tw-ease:initial
        }
    }
}
@layer theme{
    :root,:host{
        --font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
        --font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;
        --color-red-50:oklch(97.1% .013 17.38);
        --color-red-100:oklch(93.6% .032 17.717);
        --color-red-500:oklch(63.7% .237 25.331);
        --color-red-600:oklch(57.7% .245 27.325);
        --color-red-700:oklch(50.5% .213 27.518);
        --color-red-800:oklch(44.4% .177 26.899);
        --color-red-950:oklch(25.8% .092 26.042);
        --color-orange-100:oklch(95.4% .038 75.164);
        --color-orange-200:oklch(90.1% .076 70.697);
        --color-orange-400:oklch(75% .183 55.934);
        --color-orange-950:oklch(26.6% .079 36.259);
        --color-yellow-50:oklch(98.7% .026 102.212);
        --color-yellow-100:oklch(97.3% .071 103.193);
        --color-yellow-200:oklch(94.5% .129 101.54);
        --color-yellow-400:oklch(85.2% .199 91.936);
        --color-yellow-500:oklch(79.5% .184 86.047);
        --color-yellow-600:oklch(68.1% .162 75.834);
        --color-yellow-700:oklch(55.4% .135 66.442);
        --color-yellow-800:oklch(47.6% .114 61.907);
        --color-yellow-950:oklch(28.6% .066 53.813);
        --color-green-50:oklch(98.2% .018 155.826);
        --color-green-100:oklch(96.2% .044 156.743);
        --color-green-200:oklch(92.5% .084 155.995);
        --color-green-500:oklch(72.3% .219 149.579);
        --color-green-600:oklch(62.7% .194 149.214);
        --color-green-700:oklch(52.7% .154 150.069);
        --color-green-800:oklch(44.8% .119 151.328);
        --color-green-950:oklch(26.6% .065 152.934);
        --color-blue-50:oklch(97% .014 254.604);
        --color-blue-100:oklch(93.2% .032 255.585);
        --color-blue-200:oklch(88.2% .059 254.128);
        --color-blue-300:oklch(80.9% .105 251.813);
        --color-blue-400:oklch(70.7% .165 254.624);
        --color-blue-500:oklch(62.3% .214 259.815);
        --color-blue-600:oklch(54.6% .245 262.881);
        --color-blue-700:oklch(48.8% .243 264.376);
        --color-blue-800:oklch(42.4% .199 265.638);
        --color-blue-950:oklch(28.2% .091 267.935);
        --color-violet-50:oklch(96.9% .016 293.756);
        --color-violet-100:oklch(94.3% .029 294.588);
        --color-violet-200:oklch(89.4% .057 293.283);
        --color-violet-500:oklch(60.6% .25 292.717);
        --color-violet-600:oklch(54.1% .281 293.009);
        --color-violet-700:oklch(49.1% .27 292.581);
        --color-violet-800:oklch(43.2% .232 292.759);
        --color-violet-950:oklch(28.3% .141 291.089);
        --color-gray-50:oklch(98.5% .002 247.839);
        --color-gray-100:oklch(96.7% .003 264.542);
        --color-gray-200:oklch(92.8% .006 264.531);
        --color-gray-300:oklch(87.2% .01 258.338);
        --color-gray-400:oklch(70.7% .022 261.325);
        --color-gray-500:oklch(55.1% .027 264.364);
        --color-gray-600:oklch(44.6% .03 256.802);
        --color-gray-700:oklch(37.3% .034 259.733);
        --color-gray-800:oklch(27.8% .033 256.848);
        --color-gray-900:oklch(21% .034 264.665);
        --color-zinc-300:oklch(87.1% .006 286.286);
        --color-zinc-600:oklch(44.2% .017 285.786);
        --color-neutral-200:oklch(92.2% 0 0);
        --color-neutral-700:oklch(37.1% 0 0);
        --color-black:#000;
        --color-white:#fff;
        --spacing:.25rem;
        --breakpoint-sm:40rem;
        --breakpoint-md:48rem;
        --breakpoint-lg:64rem;
        --breakpoint-xl:80rem;
        --breakpoint-2xl:96rem;
        --container-2xl:42rem;
        --text-xs:.75rem;
        --text-xs--line-height:calc(1/.75);
        --text-sm:.875rem;
        --text-sm--line-height:calc(1.25/.875);
        --text-base:1rem;
        --text-base--line-height:calc(1.5/1);
        --text-lg:1.125rem;
        --text-lg--line-height:calc(1.75/1.125);
        --text-xl:1.25rem;
        --text-xl--line-height:calc(1.75/1.25);
        --text-2xl:1.5rem;
        --text-2xl--line-height:calc(2/1.5);
        --text-3xl:1.875rem;
        --text-3xl--line-height:calc(2.25/1.875);
        --text-4xl:2.25rem;
        --text-4xl--line-height:calc(2.5/2.25);
        --font-weight-normal:400;
        --font-weight-medium:500;
        --font-weight-semibold:600;
        --font-weight-bold:700;
        --tracking-tight:-.025em;
        --leading-relaxed:1.625;
        --radius-xs:.125rem;
        --radius-sm:calc(var(--radius) - 4px);
        --radius-md:calc(var(--radius) - 2px);
        --radius-lg:var(--radius);
        --radius-xl:calc(var(--radius) + 4px);
        --ease-in-out:cubic-bezier(.4,0,.2,1);
        --animate-spin:spin 1s linear infinite;
        --animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;
        --blur-md:12px;
        --aspect-video:16/9;
        --default-transition-duration:.15s;
        --default-transition-timing-function:cubic-bezier(.4,0,.2,1);
        --default-font-family:var(--font-sans);
        --default-mono-font-family:var(--font-mono);
        --color-background:var(--background);
        --color-popover:var(--popover);
        --color-muted-foreground:var(--muted-foreground);
        --color-accent:var(--accent);
        --color-primary:var(--primary);
        --color-primary-foreground:var(--primary-foreground);
        --color-secondary-foreground:var(--secondary-foreground);
        --color-destructive:var(--destructive);
        --color-mono:var(--mono);
        --color-border:var(--border);
        --color-input:var(--input);
        --text-2sm:.8125rem;
        --text-2sm--line-height:calc(1.075/.8125);
        --text-2xs:.6875rem;
        --text-2xs--line-height:calc(.825/.6875)
    }
}
@layer base{
    *,:after,:before,::backdrop{
        box-sizing:border-box;
        border:0 solid;
        margin:0;
        padding:0
    }
    ::file-selector-button{
        box-sizing:border-box;
        border:0 solid;
        margin:0;
        padding:0
    }
    html,:host{
        -webkit-text-size-adjust:100%;
        tab-size:4;
        line-height:1.5;
        font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");
        font-feature-settings:var(--default-font-feature-settings,normal);
        font-variation-settings:var(--default-font-variation-settings,normal);
        -webkit-tap-highlight-color:transparent
    }
    hr{
        height:0;
        color:inherit;
        border-top-width:1px
    }
    abbr:where([title]){
        -webkit-text-decoration:underline dotted;
        text-decoration:underline dotted
    }
    h1,h2,h3,h4,h5,h6{
        font-size:inherit;
        font-weight:inherit
    }
    a{
        color:inherit;
        -webkit-text-decoration:inherit;
        -webkit-text-decoration:inherit;
        -webkit-text-decoration:inherit;
        text-decoration:inherit
    }
    b,strong{
        font-weight:bolder
    }
    code,kbd,samp,pre{
        font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);
        font-feature-settings:var(--default-mono-font-feature-settings,normal);
        font-variation-settings:var(--default-mono-font-variation-settings,normal);
        font-size:1em
    }
    small{
        font-size:80%
    }
    sub,sup{
        vertical-align:baseline;
        font-size:75%;
        line-height:0;
        position:relative
    }
    sub{
        bottom:-.25em
    }
    sup{
        top:-.5em
    }
    table{
        text-indent:0;
        border-color:inherit;
        border-collapse:collapse
    }
    :-moz-focusring{
        outline:auto
    }
    progress{
        vertical-align:baseline
    }
    summary{
        display:list-item
    }
    ol,ul,menu{
        list-style:none
    }
    img,svg,video,canvas,audio,iframe,embed,object{
        vertical-align:middle;
        display:block
    }
    img,video{
        max-width:100%;
        height:auto
    }
    button,input,select,optgroup,textarea{
        font:inherit;
        font-feature-settings:inherit;
        font-variation-settings:inherit;
        letter-spacing:inherit;
        color:inherit;
        opacity:1;
        background-color:#0000;
        border-radius:0
    }
    ::file-selector-button{
        font:inherit;
        font-feature-settings:inherit;
        font-variation-settings:inherit;
        letter-spacing:inherit;
        color:inherit;
        opacity:1;
        background-color:#0000;
        border-radius:0
    }
    :where(select:is([multiple],[size])) optgroup{
        font-weight:bolder
    }
    :where(select:is([multiple],[size])) optgroup option{
        padding-inline-start:20px
    }
    ::file-selector-button{
        margin-inline-end:4px
    }
    ::placeholder{
        opacity:1
    }
    @supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){
        ::placeholder{
            color:currentColor
        }
        @supports (color:color-mix(in lab, red, red)){
            ::placeholder{
                color:color-mix(in oklab,currentcolor 50%,transparent)
            }
        }
    }
    textarea{
        resize:vertical
    }
    ::-webkit-search-decoration{
        -webkit-appearance:none
    }
    ::-webkit-date-and-time-value{
        min-height:1lh;
        text-align:inherit
    }
    ::-webkit-datetime-edit{
        display:inline-flex
    }
    ::-webkit-datetime-edit-fields-wrapper{
        padding:0
    }
    ::-webkit-datetime-edit{
        padding-block:0
    }
    ::-webkit-datetime-edit-year-field{
        padding-block:0
    }
    ::-webkit-datetime-edit-month-field{
        padding-block:0
    }
    ::-webkit-datetime-edit-day-field{
        padding-block:0
    }
    ::-webkit-datetime-edit-hour-field{
        padding-block:0
    }
    ::-webkit-datetime-edit-minute-field{
        padding-block:0
    }
    ::-webkit-datetime-edit-second-field{
        padding-block:0
    }
    ::-webkit-datetime-edit-millisecond-field{
        padding-block:0
    }
    ::-webkit-datetime-edit-meridiem-field{
        padding-block:0
    }
    :-moz-ui-invalid{
        box-shadow:none
    }
    button,input:where([type=button],[type=reset],[type=submit]){
        appearance:button
    }
    ::file-selector-button{
        appearance:button
    }
    ::-webkit-inner-spin-button{
        height:auto
    }
    ::-webkit-outer-spin-button{
        height:auto
    }
    [hidden]:where(:not([hidden=until-found])){
        display:none!important
    }
}
@layer components{
    .kt-avatar{
        width:calc(var(--spacing)*10);
        height:calc(var(--spacing)*10);
        flex-shrink:0;
        display:flex;
        position:relative
    }
    .kt-avatar-image{
        border-radius:3.40282e38px;
        overflow:hidden
    }
    .kt-avatar-image img{
        aspect-ratio:1;
        width:100%;
        height:100%
    }
    .kt-avatar-fallback{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--accent);
        width:100%;
        height:100%;
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        border-radius:3.40282e38px;
        justify-content:center;
        align-items:center;
        display:flex
    }
    .kt-avatar-indicator{
        width:calc(var(--spacing)*6);
        height:calc(var(--spacing)*6);
        justify-content:center;
        align-items:center;
        display:flex;
        position:absolute
    }
    .kt-avatar-status{
        width:calc(var(--spacing)*2);
        height:calc(var(--spacing)*2);
        border-style:var(--tw-border-style);
        border-width:2px;
        border-color:var(--background);
        border-radius:3.40282e38px;
        align-items:center;
        display:flex
    }
    .kt-avatar-status.kt-avatar-status-online{
        background-color:var(--color-green-600)
    }
    .kt-avatar-status.kt-avatar-status-offline{
        background-color:var(--mono)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-avatar-status.kt-avatar-status-offline{
            background-color:color-mix(in oklab,var(--mono)50%,transparent)
        }
    }
    .kt-avatar-status.kt-avatar-status-busy{
        background-color:var(--color-yellow-600)
    }
    .kt-avatar-status.kt-avatar-status-away{
        background-color:var(--color-blue-600)
    }
    .kt-accordion-item{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border)
    }
    .kt-accordion-item:last-child{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:0
    }
    .kt-accordion-toggle{
        cursor:pointer;
        justify-content:space-between;
        align-items:center;
        gap:calc(var(--spacing)*2.5);
        width:100%;
        padding-block:calc(var(--spacing)*4);
        text-align:start;
        display:flex
    }
    .kt-accordion-title{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        color:var(--mono)
    }
    .kt-accordion-content{
        transition-property:height;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        overflow:hidden
    }
    .kt-accordion-wrapper{
        padding-bottom:calc(var(--spacing)*4);
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height));
        color:var(--foreground)
    }
    .kt-accordion-indicator{
        width:calc(var(--spacing)*3);
        height:calc(var(--spacing)*3);
        color:var(--muted-foreground);
        align-items:center;
        display:inline-flex
    }
    .kt-accordion-indicator-on{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        flex-shrink:0;
        display:block
    }
    .kt-accordion-indicator-on[data-kt-accordion-item].active,[data-kt-accordion-item].active>[data-kt-accordion-toggle] .kt-accordion-indicator-on,[data-kt-accordion-item].active>[data-kt-accordion-toggle].kt-accordion-indicator-on{
        display:none
    }
    .kt-accordion-indicator-off{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        flex-shrink:0;
        display:none
    }
    .kt-accordion-indicator-off[data-kt-accordion-item].active,[data-kt-accordion-item].active>[data-kt-accordion-toggle] .kt-accordion-indicator-off,[data-kt-accordion-item].active>[data-kt-accordion-toggle].kt-accordion-indicator-off{
        display:block
    }
    .kt-accordion.kt-accordion-outline{
        gap:calc(var(--spacing)*3.5);
        flex-direction:column;
        display:flex
    }
    .kt-accordion.kt-accordion-outline .kt-accordion-item{
        border-radius:var(--radius);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border)
    }
    .kt-accordion.kt-accordion-outline .kt-accordion-content{
        border-top-style:var(--tw-border-style);
        border-top-width:1px;
        border-color:var(--border)
    }
    .kt-accordion.kt-accordion-outline .kt-accordion-toggle,.kt-accordion.kt-accordion-outline .kt-accordion-wrapper{
        padding:calc(var(--spacing)*4)
    }
    .kt-accordion-menu{
        row-gap:calc(var(--spacing)*1);
        flex-direction:column;
        width:100%;
        display:flex
    }
    .kt-accordion-menu-content{
        transition-property:height;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        padding-inline-start:calc(var(--spacing)*6);
        overflow:hidden
    }
    .kt-accordion-menu-sub{
        row-gap:calc(var(--spacing)*1);
        width:100%
    }
    .kt-accordion-menu-item{
        width:100%
    }
    :where(.kt-accordion-menu-item>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))
    }
    .kt-accordion-menu-toggle{
        align-items:center;
        column-gap:calc(var(--spacing)*2.5);
        border-radius:calc(var(--radius) - 2px);
        width:100%;
        padding-inline:calc(var(--spacing)*2.5);
        padding-block:calc(var(--spacing)*2);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        color:var(--foreground);
        display:flex
    }
    @media (hover:hover){
        .kt-accordion-menu-toggle:hover{
            background-color:var(--accent)
        }
    }
    .kt-accordion-menu-toggle:disabled{
        pointer-events:none;
        opacity:.5
    }
    .kt-accordion-menu-toggle[data-kt-accordion-item].active,[data-kt-accordion-item].active>[data-kt-accordion-toggle] .kt-accordion-menu-toggle,[data-kt-accordion-item].active>[data-kt-accordion-toggle].kt-accordion-menu-toggle{
        background-color:var(--accent)
    }
    .kt-accordion-menu-toggle svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground);
        flex-shrink:0
    }
    .kt-accordion-menu-link{
        align-items:center;
        column-gap:calc(var(--spacing)*2.5);
        border-radius:calc(var(--radius) - 2px);
        width:100%;
        padding-inline:calc(var(--spacing)*2.5);
        padding-block:calc(var(--spacing)*2);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        color:var(--foreground);
        display:flex
    }
    @media (hover:hover){
        .kt-accordion-menu-link:hover{
            background-color:var(--accent)
        }
    }
    .kt-accordion-menu-link:disabled{
        pointer-events:none;
        opacity:.5
    }
    .kt-accordion-menu-link [data-kt-accordion-initialized] .selected,[data-kt-accordion-initialized] .selected .kt-accordion-menu-link,[data-kt-accordion-initialized] .kt-accordion-menu-link.selected{
        background-color:var(--accent)
    }
    .kt-accordion-menu-link svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground);
        flex-shrink:0
    }
    .kt-accordion-menu-indicator{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        color:var(--muted-foreground);
        flex-shrink:0;
        margin-inline-start:auto
    }
    .kt-accordion-menu-indicator[data-kt-accordion-item].active,[data-kt-accordion-item].active>[data-kt-accordion-toggle] .kt-accordion-menu-indicator,[data-kt-accordion-item].active>[data-kt-accordion-toggle].kt-accordion-menu-indicator{
        rotate:180deg
    }
    [dir=rtl] .kt-accordion-menu-indicator[data-kt-accordion-item].active,[data-kt-accordion-item].active>[data-kt-accordion-toggle] :is([dir=rtl] .kt-accordion-menu-indicator),[data-kt-accordion-item].active>[data-kt-accordion-toggle]:is([dir=rtl] .kt-accordion-menu-indicator){
        rotate:-180deg
    }
    .kt-alert{
        align-items:stretch;
        width:100%;
        display:flex
    }
    .kt-alert-title{
        --tw-tracking:var(--tracking-tight);
        letter-spacing:var(--tracking-tight);
        flex-grow:1
    }
    .kt-alert-toolbar{
        align-items:center;
        gap:calc(var(--spacing)*2.5);
        display:flex
    }
    .kt-alert-description{
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-alert-description p{
        margin-bottom:calc(var(--spacing)*2);
        --tw-leading:var(--leading-relaxed);
        line-height:var(--leading-relaxed)
    }
    .kt-alert-content{
        flex-grow:1;
        width:100%
    }
    :where(.kt-alert-content>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*1.5)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*1.5)*calc(1 - var(--tw-space-y-reverse)))
    }
    .kt-alert-content .kt-alert-title{
        --tw-font-weight:var(--font-weight-semibold);
        font-weight:var(--font-weight-semibold)
    }
    .kt-alert-icon{
        flex-shrink:0
    }
    .kt-alert-close{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        cursor:pointer;
        flex-shrink:0
    }
    .kt-alert-close>svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground)
    }
    :is(.kt-alert-close:focus,.kt-alert-close:hover)>svg{
        color:var(--foreground)
    }
    .kt-alert{
        gap:calc(var(--spacing)*2.5);
        border-radius:var(--radius);
        padding:calc(var(--spacing)*3.5);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-alert .kt-alert-icon svg{
        width:calc(var(--spacing)*5);
        height:calc(var(--spacing)*5)
    }
    .kt-alert-sm{
        gap:calc(var(--spacing)*1.5);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*2.5);
        padding-block:calc(var(--spacing)*2);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .kt-alert-sm .kt-alert-close{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5)
    }
    .kt-alert-sm .kt-alert-close>svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        color:var(--muted-foreground)
    }
    .kt-alert-sm .kt-alert-icon svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4)
    }
    .kt-alert-lg{
        gap:calc(var(--spacing)*2.5);
        border-radius:calc(var(--radius) - 2px);
        padding:calc(var(--spacing)*4);
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height))
    }
    .kt-alert-lg .kt-alert-icon svg{
        width:calc(var(--spacing)*6);
        height:calc(var(--spacing)*6)
    }
    .kt-alert{
        background-color:var(--muted);
        color:var(--foreground)
    }
    .kt-alert-primary{
        background-color:var(--primary);
        color:var(--primary-foreground)
    }
    .kt-alert-primary .kt-alert-close>svg{
        color:var(--primary-foreground)
    }
    .kt-alert-destructive{
        background-color:var(--destructive);
        color:var(--destructive-foreground)
    }
    .kt-alert-destructive .kt-alert-close>svg{
        color:var(--destructive-foreground)
    }
    .kt-alert-success{
        background-color:var(--color-green-500);
        color:var(--color-white)
    }
    .kt-alert-success .kt-alert-close>svg{
        color:var(--color-white)
    }
    .kt-alert-info{
        background-color:var(--color-violet-600);
        color:var(--color-white)
    }
    .kt-alert-info .kt-alert-close>svg{
        color:var(--color-white)
    }
    .kt-alert-warning{
        background-color:var(--color-yellow-500);
        color:var(--color-white)
    }
    .kt-alert-warning .kt-alert-close>svg{
        color:var(--color-white)
    }
    .kt-alert-mono{
        background-color:var(--mono);
        color:var(--mono-foreground)
    }
    .kt-alert-mono .kt-alert-close>svg{
        color:var(--mono-foreground)
    }
    .kt-alert-mono.kt-alert-primary .kt-alert-icon{
        color:var(--primary)
    }
    .kt-alert-mono.kt-alert-success .kt-alert-icon{
        color:var(--color-green-500)
    }
    .kt-alert-mono.kt-alert-destructive .kt-alert-icon{
        color:var(--destructive)
    }
    .kt-alert-mono.kt-alert-warning .kt-alert-icon{
        color:var(--color-yellow-500)
    }
    .kt-alert-mono.kt-alert-info .kt-alert-icon{
        color:var(--color-violet-600)
    }
    .kt-alert-outline{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--background);
        color:var(--foreground)
    }
    .kt-alert-outline .kt-alert-close{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        cursor:pointer;
        flex-shrink:0
    }
    .kt-alert-outline .kt-alert-close>svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground)
    }
    :is(.kt-alert-outline .kt-alert-close:focus,.kt-alert-outline .kt-alert-close:hover)>svg{
        color:var(--foreground)
    }
    .kt-alert-outline.kt-alert-primary{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--background);
        color:var(--primary)
    }
    .kt-alert-outline.kt-alert-destructive{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--background);
        color:var(--destructive)
    }
    .kt-alert-outline.kt-alert-success{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--background);
        color:var(--color-green-500)
    }
    .kt-alert-outline.kt-alert-info{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--background);
        color:var(--color-violet-600)
    }
    .kt-alert-outline.kt-alert-warning{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--background);
        color:var(--color-yellow-500)
    }
    .kt-alert-outline.kt-alert-mono{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--background);
        color:var(--mono)
    }
    .kt-alert-light{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--muted);
        color:var(--foreground)
    }
    .kt-alert-light .kt-alert-icon>svg{
        color:var(--muted-foreground)
    }
    .kt-alert-light .kt-alert-close{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        cursor:pointer;
        flex-shrink:0
    }
    .kt-alert-light .kt-alert-close>svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground)
    }
    :is(.kt-alert-light .kt-alert-close:focus,.kt-alert-light .kt-alert-close:hover)>svg{
        color:var(--foreground)
    }
    .kt-alert-light.kt-alert-primary{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--primary);
        background-color:var(--primary);
        color:var(--foreground)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-alert-light.kt-alert-primary{
            border-color:color-mix(in oklab,var(--primary)10%,transparent);
            background-color:color-mix(in oklab,var(--primary)5%,transparent)
        }
    }
    .kt-alert-light.kt-alert-primary .kt-alert-icon>svg{
        color:var(--primary)
    }
    .kt-alert-light.kt-alert-destructive{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--destructive);
        background-color:var(--destructive);
        color:var(--foreground)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-alert-light.kt-alert-destructive{
            border-color:color-mix(in oklab,var(--destructive)10%,transparent);
            background-color:color-mix(in oklab,var(--destructive)5%,transparent)
        }
    }
    .kt-alert-light.kt-alert-destructive .kt-alert-icon>svg{
        color:var(--destructive)
    }
    .kt-alert-light.kt-alert-success{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--color-green-200);
        background-color:var(--color-green-50);
        color:var(--foreground)
    }
    .kt-alert-light.kt-alert-success:is(.dark *){
        border-color:#032e1580
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-alert-light.kt-alert-success:is(.dark *){
            border-color:color-mix(in oklab,var(--color-green-950)50%,transparent)
        }
    }
    .kt-alert-light.kt-alert-success:is(.dark *){
        background-color:#032e154d
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-alert-light.kt-alert-success:is(.dark *){
            background-color:color-mix(in oklab,var(--color-green-950)30%,transparent)
        }
    }
    .kt-alert-light.kt-alert-success .kt-alert-icon>svg{
        color:var(--color-green-500)
    }
    .kt-alert-light.kt-alert-info{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--color-violet-200);
        background-color:var(--color-violet-50);
        color:var(--foreground)
    }
    .kt-alert-light.kt-alert-info:is(.dark *){
        border-color:#2f0d6880
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-alert-light.kt-alert-info:is(.dark *){
            border-color:color-mix(in oklab,var(--color-violet-950)50%,transparent)
        }
    }
    .kt-alert-light.kt-alert-info:is(.dark *){
        background-color:#2f0d684d
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-alert-light.kt-alert-info:is(.dark *){
            background-color:color-mix(in oklab,var(--color-violet-950)30%,transparent)
        }
    }
    .kt-alert-light.kt-alert-info .kt-alert-icon>svg{
        color:var(--color-violet-500)
    }
    .kt-alert-light.kt-alert-warning{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--color-yellow-200);
        background-color:var(--color-yellow-50);
        color:var(--foreground)
    }
    .kt-alert-light.kt-alert-warning:is(.dark *){
        border-color:#43200480
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-alert-light.kt-alert-warning:is(.dark *){
            border-color:color-mix(in oklab,var(--color-yellow-950)50%,transparent)
        }
    }
    .kt-alert-light.kt-alert-warning:is(.dark *){
        background-color:#4320044d
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-alert-light.kt-alert-warning:is(.dark *){
            background-color:color-mix(in oklab,var(--color-yellow-950)30%,transparent)
        }
    }
    .kt-alert-light.kt-alert-warning .kt-alert-icon>svg{
        color:var(--color-yellow-500)
    }
    :where(.kt-form>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))
    }
    .kt-form-item{
        gap:calc(var(--spacing)*2.5);
        flex-direction:column;
        display:flex
    }
    :where(.kt-form-control>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*2.5)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*2.5)*calc(1 - var(--tw-space-y-reverse)))
    }
    .kt-form-control-inline{
        align-items:center;
        gap:calc(var(--spacing)*2.5);
        display:flex
    }
    :where(.kt-form-control-inline>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))
    }
    .kt-form-label{
        align-items:center;
        gap:calc(var(--spacing)*1);
        width:100%;
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-leading:1;
        --tw-font-weight:var(--font-weight-medium);
        line-height:1;
        font-weight:var(--font-weight-medium);
        color:var(--foreground);
        display:flex
    }
    .kt-form-label:is(:where(.peer):disabled~*),.kt-switch:disabled+.kt-form-label,.kt-checkbox:disabled+.kt-form-label,.kt-radio:disabled+.kt-form-label{
        cursor:not-allowed;
        opacity:.5
    }
    .kt-form-description{
        margin-top:calc(var(--spacing)*-1);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height));
        --tw-font-weight:var(--font-weight-normal);
        font-weight:var(--font-weight-normal);
        color:var(--secondary-foreground)
    }
    .kt-form-message{
        margin-top:calc(var(--spacing)*-1);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height));
        --tw-font-weight:var(--font-weight-normal);
        font-weight:var(--font-weight-normal);
        color:var(--destructive);
        display:none
    }
    .kt-form-actions{
        justify-content:flex-end;
        align-items:center;
        gap:calc(var(--spacing)*2.5);
        display:flex
    }
    .kt-form-item:has([aria-invalid=true]) .kt-form-message{
        display:block
    }
    .kt-form-item:has([aria-invalid=true]) .kt-form-description{
        display:none
    }
    .kt-badge{
        background-color:var(--secondary);
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        white-space:nowrap;
        color:var(--accent-foreground);
        justify-content:center;
        align-items:center;
        display:inline-flex
    }
    .kt-badge:focus{
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--ring);
        --tw-ring-offset-width:2px;
        --tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color);
        --tw-outline-style:none;
        outline-style:none
    }
    @media (forced-colors:active){
        .kt-badge:focus{
            outline-offset:2px;
            outline:2px solid #0000
        }
    }
    .kt-badge svg{
        flex-shrink:0;
        margin-inline-start:-1px
    }
    .kt-badge-primary{
        background-color:var(--primary);
        color:var(--primary-foreground)
    }
    .kt-badge-secondary{
        background-color:var(--secondary);
        color:var(--accent-foreground)
    }
    .kt-badge-success{
        background-color:var(--color-green-500);
        color:var(--color-white)
    }
    .kt-badge-warning{
        background-color:var(--color-yellow-400);
        color:var(--color-white)
    }
    .kt-badge-info{
        background-color:var(--color-violet-500);
        color:var(--color-white)
    }
    .kt-badge-mono{
        background-color:var(--mono);
        color:var(--mono-foreground)
    }
    .kt-badge-destructive{
        background-color:var(--destructive);
        color:var(--destructive-foreground)
    }
    .kt-badge-stroke{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--background);
        color:var(--secondary-foreground)
    }
    .kt-badge-disabled{
        pointer-events:none;
        opacity:.5
    }
    .kt-badge-lg{
        height:calc(var(--spacing)*7);
        min-width:calc(var(--spacing)*7);
        gap:calc(var(--spacing)*1.5);
        border-radius:calc(var(--radius) - 2px);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height));
        padding-inline:.5rem
    }
    .kt-badge-lg svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5)
    }
    .kt-badge{
        height:calc(var(--spacing)*6);
        min-width:calc(var(--spacing)*6);
        gap:calc(var(--spacing)*1.5);
        border-radius:calc(var(--radius) - 2px);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height));
        padding-inline:.45rem
    }
    .kt-badge svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5)
    }
    .kt-badge-sm{
        height:calc(var(--spacing)*5);
        min-width:calc(var(--spacing)*5);
        gap:calc(var(--spacing)*1);
        border-radius:calc(var(--radius) - 4px);
        --tw-leading:.75rem;
        padding-inline:.325rem;
        font-size:.6875rem;
        line-height:.75rem
    }
    .kt-badge-sm svg{
        width:calc(var(--spacing)*3);
        height:calc(var(--spacing)*3)
    }
    .kt-badge-xs{
        height:calc(var(--spacing)*4);
        min-width:calc(var(--spacing)*4);
        gap:calc(var(--spacing)*1);
        border-radius:calc(var(--radius) - 4px);
        --tw-leading:.5rem;
        padding-inline:.25rem;
        font-size:.625rem;
        line-height:.5rem
    }
    .kt-badge-xs svg{
        width:calc(var(--spacing)*3);
        height:calc(var(--spacing)*3)
    }
    .kt-badge-outline{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--muted);
        color:var(--secondary-foreground)
    }
    .kt-badge-outline.kt-badge-primary{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--color-blue-100);
        background-color:var(--color-blue-50);
        color:var(--color-blue-700)
    }
    .kt-badge-outline.kt-badge-primary:is(.dark *){
        border-color:var(--color-blue-950);
        background-color:#16245680
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-outline.kt-badge-primary:is(.dark *){
            background-color:color-mix(in oklab,var(--color-blue-950)50%,transparent)
        }
    }
    .kt-badge-outline.kt-badge-primary:is(.dark *){
        color:var(--color-blue-600)
    }
    .kt-badge-outline.kt-badge-secondary{
        border-color:var(--border);
        background-color:var(--secondary);
        color:var(--foreground)
    }
    .kt-badge-outline.kt-badge-secondary:is(.dark *){
        background-color:var(--secondary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-outline.kt-badge-secondary:is(.dark *){
            background-color:color-mix(in oklab,var(--secondary)50%,transparent)
        }
    }
    .kt-badge-outline.kt-badge-success{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--color-green-200);
        background-color:var(--color-green-50);
        color:var(--color-green-700)
    }
    .kt-badge-outline.kt-badge-success:is(.dark *){
        border-color:var(--color-green-950);
        background-color:#032e1580
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-outline.kt-badge-success:is(.dark *){
            background-color:color-mix(in oklab,var(--color-green-950)50%,transparent)
        }
    }
    .kt-badge-outline.kt-badge-success:is(.dark *){
        color:var(--color-green-600)
    }
    .kt-badge-outline.kt-badge-warning.kt-badge-outline{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--color-yellow-100);
        background-color:var(--color-yellow-50);
        color:var(--color-yellow-700)
    }
    .kt-badge-outline.kt-badge-warning.kt-badge-outline:is(.dark *){
        border-color:var(--color-yellow-950);
        background-color:#43200480
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-outline.kt-badge-warning.kt-badge-outline:is(.dark *){
            background-color:color-mix(in oklab,var(--color-yellow-950)50%,transparent)
        }
    }
    .kt-badge-outline.kt-badge-warning.kt-badge-outline:is(.dark *){
        color:var(--color-yellow-600)
    }
    .kt-badge-outline.kt-badge-info.kt-badge-outline{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--color-violet-100);
        background-color:var(--color-violet-50);
        color:var(--color-violet-700)
    }
    .kt-badge-outline.kt-badge-info.kt-badge-outline:is(.dark *){
        border-color:var(--color-violet-950);
        background-color:#2f0d6880
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-outline.kt-badge-info.kt-badge-outline:is(.dark *){
            background-color:color-mix(in oklab,var(--color-violet-950)50%,transparent)
        }
    }
    .kt-badge-outline.kt-badge-info.kt-badge-outline:is(.dark *){
        color:var(--color-violet-600)
    }
    .kt-badge-outline.kt-badge-mono.kt-badge-outline{
        border-color:var(--mono);
        background-color:var(--mono);
        color:var(--mono)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-outline.kt-badge-mono.kt-badge-outline{
            border-color:color-mix(in oklab,var(--mono)10%,transparent);
            background-color:color-mix(in oklab,var(--mono)10%,transparent)
        }
    }
    .kt-badge-outline.kt-badge-destructive.kt-badge-outline{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--color-red-100);
        background-color:var(--color-red-50);
        color:var(--color-red-700)
    }
    .kt-badge-outline.kt-badge-destructive.kt-badge-outline:is(.dark *){
        border-color:var(--color-red-950);
        background-color:#46080980
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-outline.kt-badge-destructive.kt-badge-outline:is(.dark *){
            background-color:color-mix(in oklab,var(--color-red-950)50%,transparent)
        }
    }
    .kt-badge-outline.kt-badge-destructive.kt-badge-outline:is(.dark *){
        color:var(--color-red-600)
    }
    .kt-badge-light.kt-badge-primary{
        background-color:var(--color-blue-100);
        color:var(--color-blue-800)
    }
    .kt-badge-light.kt-badge-primary:is(.dark *){
        background-color:#16245680
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-light.kt-badge-primary:is(.dark *){
            background-color:color-mix(in oklab,var(--color-blue-950)50%,transparent)
        }
    }
    .kt-badge-light.kt-badge-primary:is(.dark *){
        color:var(--color-blue-600)
    }
    .kt-badge-light.kt-badge-secondary{
        background-color:var(--secondary);
        color:var(--secondary-foreground)
    }
    .kt-badge-light.kt-badge-secondary:is(.dark *){
        background-color:var(--secondary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-light.kt-badge-secondary:is(.dark *){
            background-color:color-mix(in oklab,var(--secondary)50%,transparent)
        }
    }
    .kt-badge-light.kt-badge-success{
        background-color:var(--color-green-100);
        color:var(--color-green-800)
    }
    .kt-badge-light.kt-badge-success:is(.dark *){
        background-color:#032e1580
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-light.kt-badge-success:is(.dark *){
            background-color:color-mix(in oklab,var(--color-green-950)50%,transparent)
        }
    }
    .kt-badge-light.kt-badge-success:is(.dark *){
        color:var(--color-green-600)
    }
    .kt-badge-light.kt-badge-warning{
        background-color:var(--color-yellow-100);
        color:var(--color-yellow-800)
    }
    .kt-badge-light.kt-badge-warning:is(.dark *){
        background-color:#43200480
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-light.kt-badge-warning:is(.dark *){
            background-color:color-mix(in oklab,var(--color-yellow-950)50%,transparent)
        }
    }
    .kt-badge-light.kt-badge-warning:is(.dark *){
        color:var(--color-yellow-600)
    }
    .kt-badge-light.kt-badge-info{
        background-color:var(--color-violet-100);
        color:var(--color-violet-800)
    }
    .kt-badge-light.kt-badge-info:is(.dark *){
        background-color:#2f0d6880
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-light.kt-badge-info:is(.dark *){
            background-color:color-mix(in oklab,var(--color-violet-950)50%,transparent)
        }
    }
    .kt-badge-light.kt-badge-info:is(.dark *){
        color:var(--color-violet-600)
    }
    .kt-badge-light.kt-badge-mono{
        background-color:var(--mono);
        color:var(--mono)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-light.kt-badge-mono{
            background-color:color-mix(in oklab,var(--mono)10%,transparent)
        }
    }
    .kt-badge-light.kt-badge-destructive{
        background-color:var(--color-red-100);
        color:var(--color-red-800)
    }
    .kt-badge-light.kt-badge-destructive:is(.dark *){
        background-color:#46080980
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-badge-light.kt-badge-destructive:is(.dark *){
            background-color:color-mix(in oklab,var(--color-red-950)50%,transparent)
        }
    }
    .kt-badge-light.kt-badge-destructive:is(.dark *){
        color:var(--color-red-600)
    }
    .kt-badge-ghost{
        padding-inline:calc(var(--spacing)*0);
        background-color:#0000
    }
    .kt-badge-ghost.kt-badge-primary{
        color:var(--primary)
    }
    .kt-badge-ghost.kt-badge-secondary{
        color:var(--secondary-foreground)
    }
    .kt-badge-ghost.kt-badge-destructive{
        color:var(--destructive)
    }
    .kt-badge-ghost.kt-badge-success{
        color:var(--color-green-500)
    }
    .kt-badge-ghost.kt-badge-warning{
        color:var(--color-yellow-500)
    }
    .kt-badge-ghost.kt-badge-info{
        color:var(--color-violet-500)
    }
    .kt-badge-ghost.kt-badge-mono{
        color:var(--mono)
    }
    .kt-badge-btn{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        cursor:pointer;
        border-radius:calc(var(--radius) - 2px);
        padding:calc(var(--spacing)*0);
        --tw-leading:1;
        justify-content:center;
        align-items:center;
        margin-inline-end:calc(var(--spacing)*-.5);
        line-height:1;
        display:inline-flex
    }
    .kt-badge-btn>svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        opacity:.7;
        transition-property:opacity;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration))
    }
    :is(.kt-badge-btn:focus,.kt-badge-btn:hover)>svg{
        opacity:1
    }
    .kt-badge-dot{
        width:calc(var(--spacing)*1.5);
        height:calc(var(--spacing)*1.5);
        opacity:.75;
        background-color:currentColor;
        border-radius:3.40282e38px
    }
    .kt-link{
        cursor:pointer;
        border-radius:calc(var(--radius) - 2px);
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        align-items:center;
        display:inline-flex
    }
    .kt-link:focus{
        --tw-outline-style:none;
        outline-style:none
    }
    .kt-link:focus-visible{
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--color-neutral-200);
        --tw-ring-offset-width:2px;
        --tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)
    }
    .kt-link:is(.dark *):focus-visible{
        --tw-ring-color:var(--color-neutral-700)
    }
    .kt-link{
        color:var(--primary)
    }
    .kt-link-inverse{
        color:var(--background)
    }
    .kt-link-mono{
        color:var(--mono)
    }
    .kt-link{
        gap:calc(var(--spacing)*1);
        --tw-leading:var(--text-sm--line-height);
        font-size:.8125rem;
        line-height:var(--text-sm--line-height)
    }
    .kt-link svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4)
    }
    .kt-link i{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height))
    }
    .kt-link-sm{
        gap:calc(var(--spacing)*1);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .kt-link-sm svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5)
    }
    .kt-link-sm i{
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-link-lg{
        gap:calc(var(--spacing)*1.5);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-link-lg svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4)
    }
    .kt-link-lg i{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height))
    }
    .kt-link-underline{
        text-decoration-style:solid
    }
    @media (hover:hover){
        .kt-link-underline:hover{
            text-underline-offset:4px;
            text-decoration-line:underline
        }
    }
    .kt-link-underlined{
        text-underline-offset:4px;
        text-decoration-line:underline;
        text-decoration-style:solid
    }
    .kt-link-dashed{
        text-decoration-style:dashed;
        text-decoration-thickness:1px
    }
    .kt-link-disabled{
        pointer-events:none;
        opacity:.5
    }
    .kt-btn{
        cursor:pointer;
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        white-space:nowrap;
        --tw-ring-offset-color:var(--background);
        transition-property:color,box-shadow;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        --tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d;
        flex-shrink:0;
        justify-content:center;
        align-items:center;
        display:inline-flex
    }
    .kt-btn:focus-visible{
        --tw-outline-style:none;
        outline-style:none
    }
    @media (forced-colors:active){
        .kt-btn:focus-visible{
            outline-offset:2px;
            outline:2px solid #0000
        }
    }
    .kt-btn:disabled{
        pointer-events:none;
        opacity:.5;
    }
    .kt-btn:focus-visible{
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--ring);
        --tw-ring-offset-width:2px;
        --tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-btn{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-btn svg{
        flex-shrink:0
    }
    .kt-btn{
        background-color:var(--primary);
        color:var(--primary-foreground)
    }
    @media (hover:hover){
        .kt-btn:hover{
            background-color:var(--primary)
        }
        @supports (color:color-mix(in lab, red, red)){
            .kt-btn:hover{
                background-color:color-mix(in oklab,var(--primary)90%,transparent)
            }
        }
    }
    .kt-btn.active{
        background-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-btn.active{
            background-color:color-mix(in oklab,var(--primary)90%,transparent)
        }
    }
    .kt-btn-mono{
        background-color:var(--mono);
        color:var(--mono-foreground)
    }
    @media (hover:hover){
        .kt-btn-mono:hover{
            background-color:var(--mono)
        }
        @supports (color:color-mix(in lab, red, red)){
            .kt-btn-mono:hover{
                background-color:color-mix(in oklab,var(--mono)90%,transparent)
            }
        }
    }
    .kt-btn-mono.active{
        background-color:var(--mono)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-btn-mono.active{
            background-color:color-mix(in oklab,var(--mono)90%,transparent)
        }
    }
    .kt-btn-destructive{
        background-color:var(--destructive);
        color:var(--destructive-foreground)
    }
    @media (hover:hover){
        .kt-btn-destructive:hover{
            background-color:var(--destructive)
        }
        @supports (color:color-mix(in lab, red, red)){
            .kt-btn-destructive:hover{
                background-color:color-mix(in oklab,var(--destructive)90%,transparent)
            }
        }
    }
    .kt-btn-destructive.active{
        background-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-btn-destructive.active{
            background-color:color-mix(in oklab,var(--destructive)90%,transparent)
        }
    }
    .kt-btn-secondary{
        background-color:var(--secondary);
        color:var(--secondary-foreground)
    }
    @media (hover:hover){
        .kt-btn-secondary:hover{
            background-color:var(--secondary);
            color:var(--foreground)
        }
    }
    .kt-btn-secondary.active{
        background-color:var(--secondary);
        color:var(--foreground)
    }
    .kt-btn-secondary i,.kt-btn-secondary svg,:is(.kt-btn-secondary:hover,.kt-btn-secondary.active) i,:is(.kt-btn-secondary:hover,.kt-btn-secondary.active) svg{
        color:var(--muted-foreground)
    }
    .kt-btn-outline{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--input);
        background-color:var(--background);
        color:var(--secondary-foreground)
    }
    @media (hover:hover){
        .kt-btn-outline:hover{
            background-color:var(--accent);
            color:var(--accent-foreground)
        }
    }
    .kt-btn-outline.active{
        background-color:var(--accent);
        color:var(--accent-foreground)
    }
    .kt-btn-outline i,.kt-btn-outline svg{
        color:var(--muted-foreground)
    }
    :is(.kt-btn-outline:hover,.kt-btn-outline.active) i,:is(.kt-btn-outline:hover,.kt-btn-outline.active) svg{
        color:var(--secondary-foreground)
    }
    .kt-btn-outline.kt-btn-primary{
        border-color:var(--primary);
        background-color:var(--primary);
        color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-btn-outline.kt-btn-primary{
            border-color:color-mix(in oklab,var(--primary)10%,transparent);
            background-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    @media (hover:hover){
        .kt-btn-outline.kt-btn-primary:hover{
            background-color:var(--primary);
            color:var(--primary-foreground)
        }
    }
    .kt-btn-outline.kt-btn-primary.active{
        border-color:var(--primary);
        background-color:var(--primary);
        color:var(--primary-foreground)
    }
    .kt-btn-outline.kt-btn-primary i,.kt-btn-outline.kt-btn-primary svg{
        color:var(--primary)
    }
    :is(.kt-btn-outline.kt-btn-primary:hover,.kt-btn-outline.kt-btn-primary.active) i,:is(.kt-btn-outline.kt-btn-primary:hover,.kt-btn-outline.kt-btn-primary.active) svg{
        color:var(--primary-foreground)
    }
    .kt-btn-outline.kt-btn-destructive{
        border-color:var(--destructive);
        background-color:var(--destructive);
        color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-btn-outline.kt-btn-destructive{
            border-color:color-mix(in oklab,var(--destructive)10%,transparent);
            background-color:color-mix(in oklab,var(--destructive)10%,transparent)
        }
    }
    @media (hover:hover){
        .kt-btn-outline.kt-btn-destructive:hover{
            background-color:var(--destructive);
            color:var(--destructive-foreground)
        }
    }
    .kt-btn-outline.kt-btn-destructive.active{
        border-color:var(--destructive);
        background-color:var(--destructive);
        color:var(--destructive-foreground)
    }
    .kt-btn-outline.kt-btn-destructive i,.kt-btn-outline.kt-btn-destructive svg{
        color:var(--destructive)
    }
    :is(.kt-btn-outline.kt-btn-destructive:hover,.kt-btn-outline.kt-btn-destructive.active) i,:is(.kt-btn-outline.kt-btn-destructive:hover,.kt-btn-outline.kt-btn-destructive.active) svg{
        color:var(--destructive-foreground)
    }
    .kt-btn-ghost{
        color:var(--accent-foreground);
        --tw-shadow:0 0 #0000;
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        background-color:#0000
    }
    @media (hover:hover){
        .kt-btn-ghost:hover{
            background-color:var(--accent);
            color:var(--accent-foreground)
        }
    }
    .kt-btn-ghost.active{
        background-color:var(--accent);
        color:var(--accent-foreground)
    }
    .kt-btn-ghost:not(.kt-btn-primary):not(.kt-btn-destructive) i,.kt-btn-ghost:not(.kt-btn-primary):not(.kt-btn-destructive) svg{
        color:var(--muted-foreground)
    }
    :is(.kt-btn-ghost:not(.kt-btn-primary):not(.kt-btn-destructive):hover,.kt-btn-ghost:not(.kt-btn-primary):not(.kt-btn-destructive).active) i,:is(.kt-btn-ghost:not(.kt-btn-primary):not(.kt-btn-destructive):hover,.kt-btn-ghost:not(.kt-btn-primary):not(.kt-btn-destructive).active) svg{
        color:var(--secondary-foreground)
    }
    .kt-btn-ghost.kt-btn-primary{
        color:var(--primary)
    }
    @media (hover:hover){
        .kt-btn-ghost.kt-btn-primary:hover{
            background-color:var(--primary);
            color:var(--primary-foreground)
        }
    }
    .kt-btn-ghost.kt-btn-primary.active{
        background-color:var(--primary);
        color:var(--primary-foreground)
    }
    .kt-btn-ghost.kt-btn-destructive{
        color:var(--destructive)
    }
    @media (hover:hover){
        .kt-btn-ghost.kt-btn-destructive:hover{
            background-color:var(--destructive);
            color:var(--destructive-foreground)
        }
    }
    .kt-btn-ghost.kt-btn-destructive.active{
        background-color:var(--destructive);
        color:var(--destructive-foreground)
    }
    .kt-btn-dim{
        color:var(--muted-foreground);
        --tw-shadow:0 0 #0000;
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        background-color:#0000
    }
    @media (hover:hover){
        .kt-btn-dim:hover{
            color:var(--foreground);
            background-color:#0000
        }
    }
    .kt-btn-dim.active{
        color:var(--foreground);
        background-color:#0000
    }
    .kt-btn-dim i,.kt-btn-dim svg{
        color:var(--muted-foreground)
    }
    :is(.kt-btn-dim:hover,.kt-btn-dim.active) i,:is(.kt-btn-dim:hover,.kt-btn-dim.active) svg{
        color:var(--secondary-foreground)
    }
    .kt-btn{
        height:calc(var(--spacing)*11);
        gap:calc(var(--spacing)*1);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*4);
        --tw-leading:var(--text-sm--line-height);
        font-size:.875rem;
        line-height:var(--text-sm--line-height)
    }
    .kt-btn svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4)
    }
    .kt-btn i{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height))
    }
    .kt-btn.kt-btn-icon{
        width:calc(var(--spacing)*8.5);
        height:calc(var(--spacing)*8.5);
        padding:calc(var(--spacing)*0)
    }
    .kt-btn-lg{
        height:calc(var(--spacing)*9);
        gap:calc(var(--spacing)*1.5);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*4);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-btn-lg svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4)
    }
    .kt-btn-lg i{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height))
    }
    .kt-btn-lg.kt-btn-icon{
        width:calc(var(--spacing)*10);
        height:calc(var(--spacing)*10);
        padding:calc(var(--spacing)*0)
    }
    .kt-btn-sm{
        height:calc(var(--spacing)*7);
        gap:calc(var(--spacing)*1);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*2.5);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .kt-btn-sm svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5)
    }
    .kt-btn-sm i{
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-btn-sm.kt-btn-icon{
        width:calc(var(--spacing)*7);
        height:calc(var(--spacing)*7);
        padding:calc(var(--spacing)*0)
    }
    .kt-label{
        align-items:center;
        gap:calc(var(--spacing)*2);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-leading:1;
        --tw-font-weight:var(--font-weight-medium);
        line-height:1;
        font-weight:var(--font-weight-medium);
        color:var(--foreground);
        display:inline-flex
    }
    .kt-switch:disabled+.kt-label,.kt-checkbox:disabled+.kt-label,.kt-radio:disabled+.kt-label{
        cursor:not-allowed;
        opacity:.5
    }
    .kt-label-secondary{
        --tw-font-weight:var(--font-weight-normal);
        font-weight:var(--font-weight-normal)
    }
    .kt-card{
        border-radius:calc(var(--radius) + 4px);
        color:var(--card-foreground);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--card);
        --tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d;
        flex-direction:column;
        align-items:stretch;
        display:flex
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-card{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-card-header{
        min-height:calc(var(--spacing)*14);
        justify-content:space-between;
        align-items:center;
        gap:calc(var(--spacing)*2.5);
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border);
        padding-inline:calc(var(--spacing)*5);
        flex-wrap:wrap;
        display:flex
    }
    .kt-card-footer{
        border-top-style:var(--tw-border-style);
        border-top-width:1px;
        border-color:var(--border);
        padding-inline:calc(var(--spacing)*5);
        padding-block:calc(var(--spacing)*4);
        align-items:center;
        display:flex
    }
    .kt-card-content{
        padding-inline:calc(var(--spacing)*5);
        padding-block:calc(var(--spacing)*5);
        flex-grow:1
    }
    .kt-card-table{
        flex-grow:1;
        display:grid
    }
    .kt-card-table .kt-table-border{
        border-style:var(--tw-border-style);
        border-width:0
    }
    :is(.kt-card-table .kt-table th,.kt-card-table .kt-table td):first-child{
        padding-inline-start:calc(var(--spacing)*5)
    }
    :is(.kt-card-table .kt-table th,.kt-card-table .kt-table td):last-child{
        padding-inline-end:calc(var(--spacing)*5)
    }
    .kt-card-grid .kt-card-header,.kt-card-grid .kt-card-footer{
        padding-inline:calc(var(--spacing)*5)
    }
    .kt-card-grid .kt-card-content{
        padding:0
    }
    .kt-card-grid .kt-card-content .kt-table{
        border:0
    }
    .kt-card-grid .kt-card-content .kt-table th:first-child,.kt-card-grid .kt-card-content .kt-table td:first-child{
        padding-inline-start:calc(var(--spacing)*5)
    }
    :is(.kt-card-grid .kt-card-content .kt-table th:first-child,.kt-card-grid .kt-card-content .kt-table td:first-child).kt-table-cell-center,.kt-card-grid .kt-card-content .kt-table th:last-child,.kt-card-grid .kt-card-content .kt-table td:last-child{
        padding-inline-end:calc(var(--spacing)*5)
    }
    :is(.kt-card-grid .kt-card-content .kt-table th:last-child,.kt-card-grid .kt-card-content .kt-table td:last-child).table-cell-center{
        padding-inline-start:calc(var(--spacing)*5)
    }
    :where(.kt-card-heading>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))
    }
    .kt-card-toolbar{
        align-items:center;
        gap:calc(var(--spacing)*2.5);
        display:flex
    }
    .kt-card-title{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height));
        --tw-leading:1;
        --tw-font-weight:var(--font-weight-semibold);
        line-height:1;
        font-weight:var(--font-weight-semibold);
        --tw-tracking:var(--tracking-tight);
        letter-spacing:var(--tracking-tight)
    }
    .kt-card-description{
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        color:var(--muted-foreground)
    }
    .kt-card-group{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border);
        padding-inline:calc(var(--spacing)*5);
        padding-block:calc(var(--spacing)*5);
        flex-grow:1
    }
    .kt-card-group:last-child{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:0
    }
    .kt-card-group+.kt-card-footer{
        border-top-style:var(--tw-border-style);
        border-top-width:0
    }
    .kt-card-accent{
        background-color:var(--muted);
        padding:calc(var(--spacing)*1);
        --tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-card-accent{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-card-accent .kt-card-header{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:0
    }
    .kt-card-accent .kt-card-content{
        border-top-left-radius:calc(var(--radius) + 4px);
        border-top-right-radius:calc(var(--radius) + 4px);
        background-color:var(--card)
    }
    .kt-card-accent .kt-card-content:last-child{
        border-bottom-right-radius:calc(var(--radius) + 4px);
        border-bottom-left-radius:calc(var(--radius) + 4px)
    }
    .kt-card-accent .kt-card-table{
        border-radius:calc(var(--radius) + 4px);
        background-color:var(--card)
    }
    .kt-card-accent .kt-card-table:last-child{
        border-bottom-right-radius:calc(var(--radius) + 4px);
        border-bottom-left-radius:calc(var(--radius) + 4px)
    }
    .kt-card-accent .kt-card-footer{
        border-bottom-right-radius:calc(var(--radius) + 4px);
        border-bottom-left-radius:calc(var(--radius) + 4px);
        border-top-style:var(--tw-border-style);
        background-color:var(--card);
        border-top-width:0;
        margin-top:2px
    }
    .kt-card-border{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border)
    }
    .kt-card-rounded-t{
        border-top-left-radius:calc(var(--radius) + 4px);
        border-top-right-radius:calc(var(--radius) + 4px)
    }
    .kt-card-rounded-b{
        border-bottom-right-radius:calc(var(--radius) + 4px);
        border-bottom-left-radius:calc(var(--radius) + 4px)
    }
    .kt-checkbox{
        cursor:pointer;
        appearance:none;
        border-radius:calc(var(--radius) - 4px);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--input);
        background-position:50%;
        background-repeat:no-repeat;
        background-color:var(--background);
        --tw-ring-offset-color:var(--background);
        flex-shrink:0
    }
    .kt-checkbox:focus-visible{
        --tw-outline-style:none;
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--ring);
        --tw-ring-offset-width:2px;
        --tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color);
        outline-style:none
    }
    .kt-checkbox:disabled{
        cursor:not-allowed;
        opacity:.5
    }
    .kt-checkbox[aria-invalid=true]{
        border-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-checkbox[aria-invalid=true]{
            border-color:color-mix(in oklab,var(--destructive)60%,transparent)
        }
    }
    .kt-checkbox[aria-invalid=true]{
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-checkbox[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)10%,transparent)
        }
    }
    .kt-checkbox:checked,.kt-checkbox:indeterminate{
        border-color:var(--primary);
        background-color:var(--primary);
        color:var(--primary-foreground)
    }
    .kt-checkbox-mono:checked,.kt-checkbox-mono:indeterminate{
        border-color:var(--mono);
        background-color:var(--mono);
        color:var(--mono-foreground)
    }
    .kt-checkbox:checked,.kt-checkbox[aria-checked=true]{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='9' viewBox='0 0 12 9' fill='none'%3E%3Cpath d='M10.3667 0.541643L4.80007 6.10831L1.56674 2.87498C1.41061 2.71977 1.1994 2.63265 0.979241 2.63265C0.759086 2.63265 0.547876 2.71977 0.391741 2.87498C0.236532 3.03111 0.149414 3.24232 0.149414 3.46248C0.149414 3.68263 0.236532 3.89384 0.391741 4.04998L4.21674 7.87498C4.37288 8.03019 4.58409 8.1173 4.80424 8.1173C5.0244 8.1173 5.23561 8.03019 5.39174 7.87498L11.5417 1.72498C11.6198 1.64751 11.6818 1.55534 11.7241 1.45379C11.7665 1.35224 11.7882 1.24332 11.7882 1.13331C11.7882 1.0233 11.7665 0.914379 11.7241 0.81283C11.6818 0.711281 11.6198 0.619113 11.5417 0.541643C11.3856 0.386434 11.1744 0.299316 10.9542 0.299316C10.7341 0.299316 10.5229 0.386434 10.3667 0.541643Z' fill='white'/%3E%3C/svg%3E")
    }
    .kt-checkbox:indeterminate{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 10h8'/%3E%3C/svg%3E")
    }
    .kt-checkbox{
        width:calc(var(--spacing)*5);
        height:calc(var(--spacing)*5)
    }
    .kt-checkbox-sm{
        width:calc(var(--spacing)*4.5);
        height:calc(var(--spacing)*4.5)
    }
    .kt-checkbox-sm:checked,.kt-checkbox-sm[aria-checked=true]{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='7' viewBox='0 0 10 7' fill='none'%3E%3Cpath d='M8.4932 0.233321L4.03986 4.68665L1.4532 2.09999C1.32829 1.97582 1.15932 1.90613 0.983198 1.90613C0.807074 1.90613 0.638106 1.97582 0.513198 2.09999C0.38903 2.2249 0.319336 2.39386 0.319336 2.56999C0.319336 2.74611 0.38903 2.91508 0.513198 3.03999L3.5732 6.09999C3.69811 6.22415 3.86707 6.29385 4.0432 6.29385C4.21932 6.29385 4.38829 6.22415 4.5132 6.09999L9.4332 1.17999C9.49568 1.11801 9.54528 1.04428 9.57912 0.963038C9.61297 0.881799 9.6304 0.794662 9.6304 0.706655C9.6304 0.618647 9.61297 0.53151 9.57912 0.45027C9.54528 0.369031 9.49568 0.295296 9.4332 0.233321C9.30829 0.109154 9.13932 0.0394592 8.9632 0.0394592C8.78707 0.0394592 8.61811 0.109154 8.4932 0.233321Z' fill='white'/%3E%3C/svg%3E")
    }
    .kt-checkbox-sm:indeterminate{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 10h8'/%3E%3C/svg%3E")
    }
    .kt-checkbox-lg{
        width:calc(var(--spacing)*5.5);
        height:calc(var(--spacing)*5.5)
    }
    .kt-checkbox-lg:checked,.kt-checkbox-lg[aria-checked=true]{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='10' viewBox='0 0 14 10' fill='none'%3E%3Cpath d='M11.8035 1.19582L5.68018 7.31915L2.12351 3.76249C1.95176 3.59176 1.71943 3.49593 1.47726 3.49593C1.23509 3.49593 1.00276 3.59176 0.831013 3.76249C0.660283 3.93424 0.564453 4.16657 0.564453 4.40874C0.564453 4.65091 0.660283 4.88324 0.831013 5.05499L5.03851 9.26249C5.21026 9.43322 5.44259 9.52905 5.68476 9.52905C5.92693 9.52905 6.15926 9.43322 6.33101 9.26249L13.096 2.49749C13.1819 2.41227 13.2501 2.31089 13.2967 2.19918C13.3432 2.08748 13.3672 1.96766 13.3672 1.84665C13.3672 1.72564 13.3432 1.60583 13.2967 1.49413C13.2501 1.38242 13.1819 1.28104 13.096 1.19582C12.9243 1.02509 12.6919 0.92926 12.4498 0.92926C12.2076 0.92926 11.9753 1.02509 11.8035 1.19582Z' fill='white'/%3E%3C/svg%3E")
    }
    .kt-checkbox-lg:indeterminate{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 10h8'/%3E%3C/svg%3E")
    }
    .dark .kt-checkbox[aria-invalid=true]{
        border-color:var(--destructive);
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark .kt-checkbox[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)
        }
    }
    .kt-datatable-toolbar{
        justify-content:center;
        gap:calc(var(--spacing)*3);
        border-top-style:var(--tw-border-style);
        border-top-width:1px;
        border-color:var(--border);
        padding-inline:calc(var(--spacing)*4);
        padding-block:calc(var(--spacing)*3);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        color:var(--muted-foreground);
        flex-direction:column;
        display:flex
    }
    @media (min-width:40rem){
        .kt-datatable-toolbar{
            flex-direction:row;
            justify-content:space-between
        }
    }
    .kt-datatable-pagination{
        align-items:center;
        gap:calc(var(--spacing)*1);
        display:flex
    }
    .kt-datatable-pagination .kt-datatable-pagination-button{
        height:calc(var(--spacing)*7);
        min-width:calc(var(--spacing)*7);
        cursor:pointer;
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*.5);
        color:var(--muted-foreground);
        background-color:#0000;
        justify-content:center;
        align-items:center;
        display:inline-flex
    }
    .kt-datatable-pagination .kt-datatable-pagination-button.active,.kt-datatable-pagination .kt-datatable-pagination-button:hover:not(:disabled){
        background-color:var(--accent);
        color:var(--accent-foreground)
    }
    .kt-datatable-pagination .kt-datatable-pagination-button.kt-datatable-pagination-prev,.kt-datatable-pagination .kt-datatable-pagination-button.kt-datatable-pagination-next{
        color:var(--foreground)
    }
    .kt-datatable-pagination .kt-datatable-pagination-button:disabled{
        cursor:default;
        color:var(--muted-foreground)
    }
    .kt-datatable-length{
        align-items:center;
        gap:calc(var(--spacing)*2);
        white-space:nowrap;
        display:flex
    }
    .kt-datatable-info{
        align-items:center;
        gap:calc(var(--spacing)*4);
        display:flex
    }
    .kt-datatable-loading{
        align-items:center;
        gap:calc(var(--spacing)*2);
        border-radius:calc(var(--radius) - 2px);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--card);
        padding-inline:calc(var(--spacing)*4);
        padding-block:calc(var(--spacing)*2);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-leading:1;
        --tw-font-weight:var(--font-weight-medium);
        line-height:1;
        font-weight:var(--font-weight-medium);
        color:var(--card-foreground);
        --tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d;
        display:flex
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-datatable-loading{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-datatable-overlay{
        z-index:10;
        background-color:#ffffffb3;
        justify-content:center;
        align-items:center;
        width:100%;
        height:100%;
        display:flex;
        position:absolute;
        top:0;
        left:0
    }
    [data-kt-datatable]{
        position:relative
    }
    [data-kt-datatable].loading table{
        opacity:.6
    }
    .kt-drawer{
        inset:calc(var(--spacing)*0);
        z-index:10;
        background-color:var(--popover);
        color:var(--popover-foreground);
        --tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        --tw-duration:.3s;
        --tw-ease:var(--ease-in-out);
        transition-duration:.3s;
        transition-timing-function:var(--ease-in-out);
        --tw-outline-style:none;
        outline-style:none;
        flex-direction:column;
        position:fixed
    }
    .kt-drawer-backdrop{
        inset:calc(var(--spacing)*0);
        -webkit-backdrop-filter:blur(4px);
        backdrop-filter:blur(4px);
        transition-property:all;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        --tw-duration:.3s;
        background-color:#0000004d;
        transition-duration:.3s;
        position:fixed
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-drawer-backdrop{
            background-color:color-mix(in oklab,var(--color-black)30%,transparent)
        }
    }
    .kt-drawer-header{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border);
        padding:calc(var(--spacing)*5);
        justify-content:space-between;
        align-items:center;
        display:flex
    }
    .kt-drawer-title{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height));
        --tw-font-weight:var(--font-weight-semibold);
        font-weight:var(--font-weight-semibold);
        color:var(--mono)
    }
    .kt-drawer-close{
        width:calc(var(--spacing)*6);
        height:calc(var(--spacing)*6);
        cursor:pointer;
        flex-shrink:0;
        margin-inline-end:calc(var(--spacing)*-2.5)
    }
    .kt-drawer-close i{
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height));
        opacity:.7
    }
    .kt-drawer-close>svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        opacity:.7
    }
    :is(.kt-drawer-close:focus,.kt-drawer-close:hover) i,:is(.kt-drawer-close:focus,.kt-drawer-close:hover)>svg{
        opacity:1
    }
    .kt-drawer-content{
        padding:calc(var(--spacing)*5);
        flex-grow:1;
        overflow-y:auto
    }
    .kt-drawer-footer{
        border-top-style:var(--tw-border-style);
        border-top-width:1px;
        border-color:var(--border);
        padding:calc(var(--spacing)*5);
        justify-content:space-between;
        align-items:center;
        display:flex
    }
    .kt-drawer-start{
        --tw-translate-x:-100%;
        max-width:90%;
        translate:var(--tw-translate-x)var(--tw-translate-y);
        inset-inline-end:auto
    }
    .kt-drawer-start[data-kt-drawer-initialized].open,[data-kt-drawer-initialized].open .kt-drawer-start{
        --tw-translate-x:calc(var(--spacing)*0);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .kt-drawer-end{
        --tw-translate-x:100%;
        max-width:90%;
        translate:var(--tw-translate-x)var(--tw-translate-y);
        inset-inline-start:auto
    }
    .kt-drawer-end[data-kt-drawer-initialized].open,[data-kt-drawer-initialized].open .kt-drawer-end{
        --tw-translate-x:calc(var(--spacing)*0);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .kt-drawer-top{
        inset-inline-start:calc(var(--spacing)*0);
        inset-inline-end:calc(var(--spacing)*0);
        top:auto;
        bottom:calc(var(--spacing)*0);
        --tw-translate-y:100%;
        max-height:90%;
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .kt-drawer-top[data-kt-drawer-initialized].open,[data-kt-drawer-initialized].open .kt-drawer-top{
        --tw-translate-y:calc(var(--spacing)*0);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .kt-drawer-bottom{
        inset-inline-start:calc(var(--spacing)*0);
        inset-inline-end:calc(var(--spacing)*0);
        top:calc(var(--spacing)*0);
        --tw-translate-y:-100%;
        max-height:90%;
        translate:var(--tw-translate-x)var(--tw-translate-y);
        bottom:auto
    }
    .kt-drawer-bottom[data-kt-drawer-initialized].open,[data-kt-drawer-initialized].open .kt-drawer-bottom{
        --tw-translate-y:calc(var(--spacing)*0);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .kt-drawer.open .kt-drawer-start,.kt-drawer.open .kt-drawer-end{
        --tw-translate-x:calc(var(--spacing)*0);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .kt-drawer.open .kt-drawer-top,.kt-drawer.open .kt-drawer-bottom{
        --tw-translate-y:calc(var(--spacing)*0);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .kt-dropdown{
        border-radius:calc(var(--radius) - 2px);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--popover);
        color:var(--popover-foreground);
        --tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-dropdown{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-dropdown:not(.open){
        display:none
    }
    .kt-dropdown-header{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border);
        padding-inline:calc(var(--spacing)*4);
        padding-block:calc(var(--spacing)*3);
        --tw-font-weight:var(--font-weight-semibold);
        font-weight:var(--font-weight-semibold)
    }
    .kt-dropdown-body{
        padding-inline:calc(var(--spacing)*4);
        padding-block:calc(var(--spacing)*3)
    }
    .kt-dropdown-menu{
        border-radius:calc(var(--radius) - 2px);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--popover);
        padding:calc(var(--spacing)*2);
        color:var(--popover-foreground);
        --tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d
    }
    :where(.kt-dropdown-menu>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-dropdown-menu{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-dropdown-menu:not(.open){
        display:none
    }
    .kt-dropdown-menu-sub{
        width:100%
    }
    :where(.kt-dropdown-menu-sub>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))
    }
    .kt-dropdown-menu-toggle{
        cursor:pointer;
        align-items:center;
        column-gap:calc(var(--spacing)*2.5);
        border-radius:calc(var(--radius) - 2px);
        width:100%;
        padding-inline:calc(var(--spacing)*2.5);
        padding-block:calc(var(--spacing)*2);
        text-align:start;
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        color:var(--foreground);
        display:flex
    }
    .kt-dropdown-menu-toggle:disabled{
        pointer-events:none;
        opacity:.5
    }
    @media (hover:hover){
        .kt-dropdown-menu-toggle:hover{
            background-color:var(--accent);
            color:var(--accent-foreground)
        }
    }
    [data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle].kt-dropdown-menu-toggle,[data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle] .kt-dropdown-menu-toggle{
        background-color:var(--accent);
        color:var(--accent-foreground)
    }
    .kt-dropdown-menu-toggle .kt-dropdown-menu-indicator{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        color:var(--muted-foreground);
        flex-shrink:0;
        align-items:center;
        margin-inline-start:auto;
        display:inline-flex
    }
    .kt-dropdown-menu-toggle i{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height));
        color:var(--muted-foreground);
        flex-shrink:0
    }
    .kt-dropdown-menu-toggle svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground);
        flex-shrink:0
    }
    .kt-dropdown-menu-link{
        cursor:pointer;
        align-items:center;
        column-gap:calc(var(--spacing)*2.5);
        border-radius:calc(var(--radius) - 2px);
        width:100%;
        padding-inline:calc(var(--spacing)*2.5);
        padding-block:calc(var(--spacing)*2);
        text-align:start;
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        color:var(--foreground);
        display:flex
    }
    .kt-dropdown-menu-link:disabled{
        pointer-events:none;
        opacity:.5
    }
    @media (hover:hover){
        .kt-dropdown-menu-link:hover{
            background-color:var(--accent);
            color:var(--accent-foreground)
        }
    }
    .kt-dropdown-menu-link [data-kt-dropdown-initialized] .selected,[data-kt-dropdown-initialized] .selected .kt-dropdown-menu-link,[data-kt-dropdown-initialized] .kt-dropdown-menu-link.selected{
        background-color:var(--accent);
        color:var(--accent-foreground)
    }
    .kt-dropdown-menu-link i{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height));
        color:var(--muted-foreground);
        flex-shrink:0
    }
    .kt-dropdown-menu-link svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground);
        flex-shrink:0
    }
    .kt-dropdown-menu-separator{
        margin-inline:calc(var(--spacing)*-2);
        margin-block:calc(var(--spacing)*2.5);
        background-color:var(--border);
        height:1px
    }
    .kt-dropdown-menu-separator:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        rotate:180deg;
        transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)
    }
    .kt-image-input{
        width:calc(var(--spacing)*20);
        height:calc(var(--spacing)*20);
        cursor:pointer;
        justify-content:center;
        align-items:center;
        display:inline-flex;
        position:relative
    }
    .kt-image-input input[type=file]{
        width:calc(var(--spacing)*0);
        height:calc(var(--spacing)*0);
        appearance:none;
        opacity:0;
        position:absolute
    }
    .kt-image-input-remove{
        inset-inline-end:calc(var(--spacing)*.25);
        top:calc(var(--spacing)*.25);
        z-index:1;
        width:calc(var(--spacing)*5);
        height:calc(var(--spacing)*5);
        cursor:pointer;
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--background);
        --tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        border-radius:3.40282e38px;
        justify-content:center;
        align-items:center;
        display:flex;
        position:absolute
    }
    .kt-image-input-remove i{
        color:var(--muted-foreground);
        font-size:11px
    }
    .kt-image-input-remove svg{
        width:calc(var(--spacing)*3.25);
        height:calc(var(--spacing)*3.25);
        color:var(--muted-foreground)
    }
    .kt-image-input-remove:hover i,.kt-image-input-remove:hover svg{
        color:var(--foreground)
    }
    .kt-image-input-placeholder{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-repeat:no-repeat;
        background-size:cover;
        border-radius:3.40282e38px;
        width:100%;
        height:100%;
        position:relative;
        overflow:hidden
    }
    [data-kt-image-input-initialized].empty .kt-image-input-placeholder{
        border-color:var(--border)
    }
    .kt-image-input-preview{
        background-repeat:no-repeat;
        background-size:cover;
        border-radius:3.40282e38px;
        width:100%;
        height:100%;
        position:relative;
        overflow:hidden
    }
    .kt-input{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--input);
        background-color:var(--background);
        width:100%;
        color:var(--foreground);
        --tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d;
        outline-style:var(--tw-outline-style);
        transition-property:color,box-shadow;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        outline-width:0;
        display:block
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-input{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-input::placeholder{
        color:var(--muted-foreground)
    }
    .kt-input:focus-visible{
        border-color:var(--ring);
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--ring)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-input:focus-visible{
            --tw-ring-color:color-mix(in oklab,var(--ring)30%,transparent)
        }
    }
    .kt-input:focus-visible{
        --tw-outline-style:none;
        outline-style:none
    }
    .kt-input::file-selector-button{
        height:100%
    }
    .kt-input:disabled{
        cursor:not-allowed;
        opacity:.7;
        background : var(--muted);
    }
    .kt-input[readonly]{
        opacity:.7;
        background: var(--muted);
    }
    .kt-input[type=file]{
        padding-block:calc(var(--spacing)*0)
    }
    .kt-input::file-selector-button{
        border-style:var(--tw-border-style);
        border-width:0
    }
    .kt-input::file-selector-button{
        border-inline-end-style:var(--tw-border-style);
        border-inline-end-width:1px
    }
    .kt-input::file-selector-button{
        --tw-border-style:solid;
        border-style:solid
    }
    .kt-input::file-selector-button{
        border-color:var(--input)
    }
    .kt-input::file-selector-button{
        background-color:#0000
    }
    .kt-input::file-selector-button{
        padding:calc(var(--spacing)*0)
    }
    .kt-input::file-selector-button{
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium)
    }
    .kt-input::file-selector-button{
        color:var(--foreground)
    }
    .kt-input::file-selector-button{
        font-style:normal
    }
    .kt-input[aria-invalid=true]{
        border-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-input[aria-invalid=true]{
            border-color:color-mix(in oklab,var(--destructive)60%,transparent)
        }
    }
    .kt-input[aria-invalid=true]{
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-input[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)10%,transparent)
        }
    }
    .kt-input{
        height:calc(var(--spacing)*9);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*3);
        --tw-leading:var(--text-sm--line-height);
        font-size:.85rem;
        line-height:var(--text-sm--line-height)
    }
    .kt-input::file-selector-button{
        margin-inline-end:calc(var(--spacing)*3)
    }
    .kt-input::file-selector-button{
        padding-inline-end:calc(var(--spacing)*3)
    }
    .kt-input-lg{
        height:calc(var(--spacing)*10);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*4);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-input-lg::file-selector-button{
        margin-inline-end:calc(var(--spacing)*4)
    }
    .kt-input-lg::file-selector-button{
        padding-inline-end:calc(var(--spacing)*4)
    }
    .kt-input-sm{
        height:calc(var(--spacing)*7);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*2.5);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .kt-input-sm::file-selector-button{
        margin-inline-end:calc(var(--spacing)*2.5)
    }
    .kt-input-sm::file-selector-button{
        padding-inline-end:calc(var(--spacing)*2.5)
    }
    .kt-input:not(input){
        align-items:center;
        gap:calc(var(--spacing)*1.5);
        display:flex
    }
    .kt-input:not(input):has(input:focus-visible){
        border-color:var(--ring);
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--ring);
        --tw-outline-style:none;
        outline-style:none
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-input:not(input):has(input:focus-visible){
            --tw-ring-color:color-mix(in oklab,var(--ring)30%,transparent)
        }
    }
    .kt-input:not(input) input{
        border-style:var(--tw-border-style);
        width:100%;
        height:auto;
        padding:calc(var(--spacing)*0);
        color:var(--foreground);
        --tw-shadow:0 0 #0000;
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        transition-property:color;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        --tw-outline-style:none;
        font-size:inherit;
        list-style:inherit;
        background-color:#0000;
        border-width:0;
        outline-style:none;
        display:flex
    }
    .kt-input:not(input) input::placeholder{
        color:var(--muted-foreground)
    }
    .kt-input:not(input) input:focus-visible{
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
    }
    .kt-input:not(input) input:disabled{
        cursor:not-allowed;
        opacity:.5
    }
    .kt-input:not(input) i{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height));
        color:var(--muted-foreground)
    }
    .kt-input:not(input) svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground)
    }
    .kt-input:not(input).kt-input-sm{
        gap:calc(var(--spacing)*1.25)
    }
    .kt-input:not(input).kt-input-sm i{
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-input:not(input).kt-input-sm svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        color:var(--muted-foreground)
    }
    .kt-input:not(input).kt-input-lg{
        gap:calc(var(--spacing)*1.5)
    }
    .kt-input:not(input).kt-input-lg i{
        font-size:var(--text-lg);
        line-height:var(--tw-leading,var(--text-lg--line-height))
    }
    .kt-input:not(input).kt-input-lg svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground)
    }
    .kt-input-addon{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--input);
        background-color:var(--muted);
        color:var(--secondary-foreground);
        --tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d;
        height:calc(var(--spacing)*8.5);
        min-width:calc(var(--spacing)*8.5);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*3);
        --tw-leading:var(--text-sm--line-height);
        font-size:.8125rem;
        line-height:var(--text-sm--line-height);
        flex-shrink:0;
        justify-content:center;
        align-items:center;
        display:flex
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-input-addon{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-input-addon i{
        font-size:var(--text-lg);
        line-height:var(--tw-leading,var(--text-lg--line-height))
    }
    .kt-input-addon svg{
        width:calc(var(--spacing)*4.5);
        height:calc(var(--spacing)*4.5)
    }
    .kt-input-addon.kt-input-addon-sm{
        height:calc(var(--spacing)*7);
        min-width:calc(var(--spacing)*7);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*2.5);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .kt-input-addon.kt-input-addon-sm i{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height))
    }
    .kt-input-addon.kt-input-addon-sm svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5)
    }
    .kt-input-addon.kt-input-addon-lg{
        height:calc(var(--spacing)*10);
        min-width:calc(var(--spacing)*10);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*4);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-input-addon.kt-input-addon-lg i{
        font-size:var(--text-lg);
        line-height:var(--tw-leading,var(--text-lg--line-height))
    }
    .kt-input-addon.kt-input-addon-lg svg{
        width:calc(var(--spacing)*4.5);
        height:calc(var(--spacing)*4.5)
    }
    .kt-input-addon.kt-input-addon-icon{
        padding-inline:calc(var(--spacing)*0)
    }
    .kt-input-ghost{
        border-style:var(--tw-border-style);
        background-color:var(--background);
        padding:calc(var(--spacing)*0);
        --tw-shadow:0 0 #0000;
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        border-width:0
    }
    .kt-input-ghost:focus-visible{
        border-style:var(--tw-border-style);
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        border-width:0
    }
    .dark .kt-input[aria-invalid=true]{
        border-color:var(--destructive);
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark .kt-input[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)
        }
    }
    .kt-input-group{
        align-items:stretch;
        display:flex
    }
    .kt-input-group .kt-input{
        flex-grow:1
    }
    .kt-input-group .kt-input-addon:has(+.kt-input){
        border-inline-end-style:var(--tw-border-style)!important;
        border-inline-end-width:0!important;
        border-start-end-radius:0!important;
        border-end-end-radius:0!important
    }
    .kt-input-group .kt-input+.kt-input-addon{
        border-inline-start-style:var(--tw-border-style)!important;
        border-inline-start-width:0!important;
        border-start-start-radius:0!important;
        border-end-start-radius:0!important
    }
    .kt-input-group .kt-input-addon:has(+.kt-btn){
        border-start-end-radius:0!important;
        border-end-end-radius:0!important
    }
    .kt-input-group .kt-input+.kt-btn,.kt-input-group .kt-btn+.kt-input,.kt-input-group .kt-input-addon+.kt-input{
        border-start-start-radius:0!important;
        border-end-start-radius:0!important
    }
    .kt-input-group .kt-input:has(+.kt-btn),.kt-input-group .kt-input:has(+.kt-input-addon){
        border-start-end-radius:0!important;
        border-end-end-radius:0!important
    }
    .kt-modal{
        inset:calc(var(--spacing)*0);
        padding:calc(var(--spacing)*4);
        position:fixed;
        overflow:auto;
        height: 100%;
    }
    .kt-modal-center{
        top: calc(50%);
        --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    .kt-modal:not(.open){
        display:none
    }
    .kt-modal-backdrop{
        inset:calc(var(--spacing)*0);
        -webkit-backdrop-filter:blur(4px);
        backdrop-filter:blur(4px);
        transition-property:all;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        --tw-duration:.3s;
        background-color:#0000004d;
        transition-duration:.3s;
        position:fixed
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-modal-backdrop{
            background-color:color-mix(in oklab,var(--color-black)30%,transparent)
        }
    }
    .kt-modal-close{
        width:calc(var(--spacing)*6);
        height:calc(var(--spacing)*6);
        cursor:pointer;
        flex-shrink:0;
        margin-inline-end:calc(var(--spacing)*-2.5)
    }
    .kt-modal-close>svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        opacity:.7
    }
    :is(.kt-modal-close:focus,.kt-modal-close:hover)>svg{
        opacity:1
    }
    .kt-modal-dialog{
        --tw-translate-x:calc(calc(1/2*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y);
        --tw-translate-y:calc(calc(1/2*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y);
        padding:calc(var(--spacing)*4);
        inset-inline-start:50%;
        display:none;
        position:fixed;
        top:50%;
        overflow:auto
    }
    .kt-modal-content{
        border-radius:var(--radius);
        background-color:var(--popover);
        color:var(--popover-foreground);
        --tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        flex-direction:column;
        margin-inline:auto;
        display:flex;
        position:relative
    }
    .kt-modal-header{
        justify-content:space-between;
        align-items:center;
        gap:calc(var(--spacing)*2);
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border);
        padding-inline:calc(var(--spacing)*5);
        padding-block:calc(var(--spacing)*4);
        display:flex
    }
    .kt-modal-title{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height));
        --tw-font-weight:var(--font-weight-semibold);
        font-weight:var(--font-weight-semibold);
        color:var(--mono)
    }
    .kt-modal-body{
        padding-inline:calc(var(--spacing)*5);
        padding-block:calc(var(--spacing)*4);
        overflow-y:auto
    }
    .kt-modal-footer{
        justify-content:space-between;
        align-items:center;
        gap:calc(var(--spacing)*2);
        border-top-style:var(--tw-border-style);
        border-top-width:1px;
        border-color:var(--border);
        padding-inline:calc(var(--spacing)*5);
        padding-block:calc(var(--spacing)*4);
        display:flex
    }
    .kt-radio{
        cursor:pointer;
        appearance:none;
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--input);
        background-position:50%;
        background-repeat:no-repeat;
        background-color:var(--background);
        --tw-ring-offset-color:var(--background);
        border-radius:3.40282e38px;
        flex-shrink:0
    }
    .kt-radio:focus-visible{
        --tw-outline-style:none;
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--ring);
        --tw-ring-offset-width:2px;
        --tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color);
        outline-style:none
    }
    .kt-radio:disabled{
        cursor:not-allowed;
        opacity:.5
    }
    .kt-radio[aria-invalid=true]{
        border-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-radio[aria-invalid=true]{
            border-color:color-mix(in oklab,var(--destructive)60%,transparent)
        }
    }
    .kt-radio[aria-invalid=true]{
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-radio[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)10%,transparent)
        }
    }
    .kt-radio:is(.dark *)[aria-invalid=true]{
        border-color:var(--destructive);
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-radio:is(.dark *)[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)
        }
    }
    .kt-radio:checked,.kt-radio:indeterminate{
        border-color:var(--primary);
        background-color:var(--primary);
        color:var(--primary-foreground)
    }
    .kt-radio:checked,.kt-radio[aria-checked=true]{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 22 22' fill='none'%3E%3Ccircle cx='10.9995' cy='11' r='5.86667' fill='white'/%3E%3C/svg%3E")
    }
    .kt-radio{
        width:calc(var(--spacing)*5);
        height:calc(var(--spacing)*5)
    }
    .kt-radio-sm{
        width:calc(var(--spacing)*4.5);
        height:calc(var(--spacing)*4.5)
    }
    .kt-radio-lg{
        width:calc(var(--spacing)*5.5);
        height:calc(var(--spacing)*5.5)
    }
    .kt-radio-mono:checked,.kt-radio-mono:indeterminate{
        border-color:var(--mono);
        background-color:var(--mono);
        color:var(--mono-foreground)
    }
    .dark .kt-radio[aria-invalid=true]{
        border-color:var(--destructive);
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark .kt-radio[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)
        }
    }
    :root{
        --kt-scrollable-scrollbar-size:5px;
        --kt-scrollable-thumb-color:var(--color-accent)
    }
    .kt-scrollable::-webkit-scrollbar{
        width:var(--kt-scrollable-scrollbar-size)
    }
    .kt-scrollable::-webkit-scrollbar-track{
        background:0 0
    }
    .kt-scrollable::-webkit-scrollbar-thumb{
        background:var(--kt-scrollable-thumb-color);
        border-radius:var(--kt-scrollable-scrollbar-size)
    }
    .kt-scrollable{
        scrollbar-width:thin;
        scrollbar-color:var(--kt-scrollable-thumb-color)transparent
    }
    .kt-select{
        appearance:none;
        align-items:center;
        gap:calc(var(--spacing)*2);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--input);
        background-color:var(--background);
        width:100%;
        padding-block:calc(var(--spacing)*0);
        color:var(--foreground);
        --tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d;
        transition-property:color,box-shadow;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%239f9fa9' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
        background-position:right .65rem center;
        background-repeat:no-repeat;
        background-size:14px 11px;
        display:flex
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-select{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-select::placeholder{
        color:var(--muted-foreground)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-select::placeholder{
            color:color-mix(in oklab,var(--muted-foreground)80%,transparent)
        }
    }
    .kt-select:focus-visible{
        border-color:var(--ring);
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--ring)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-select:focus-visible{
            --tw-ring-color:color-mix(in oklab,var(--ring)30%,transparent)
        }
    }
    .kt-select:focus-visible{
        --tw-outline-style:none;
        outline-style:none
    }
    .kt-select::file-selector-button{
        height:100%
    }
    .kt-select:disabled{
        cursor:not-allowed;
        opacity:.7;
        background-color: var(--muted);
    }
    .kt-select[readonly]{
        opacity:.7
    }
    .kt-select[type=file]{
        padding-block:calc(var(--spacing)*0)
    }
    .kt-select[aria-invalid=true]{
        border-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-select[aria-invalid=true]{
            border-color:color-mix(in oklab,var(--destructive)60%,transparent)
        }
    }
    .kt-select[aria-invalid=true]{
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-select[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)10%,transparent)
        }
    }
    .dark .kt-select{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%239f9fa9' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E")
    }
    .kt-select[multiple],.kt-select[size]:not([size="1"]){
        background-image:none;
        padding-inline-end:var(--btn-default-px)
    }
    .kt-select:-moz-focusring{
        color:#0000;
        text-shadow:none
    }
    [dir=rtl] .kt-select{
        background-position:.5rem
    }
    .kt-select.active{
        border-color:var(--ring);
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--ring);
        --tw-outline-style:none;
        outline-style:none
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-select.active{
            --tw-ring-color:color-mix(in oklab,var(--ring)30%,transparent)
        }
    }
    .kt-select.disabled{
        cursor:not-allowed;
        opacity:.7;
        background-color: var(--muted);
    }
    .kt-select-arrow{
        margin-inline-start:auto
    }
    .kt-select-arrow svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground)
    }
    .kt-select-dropdown{
        border-radius:calc(var(--radius) - 2px);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--popover);
        color:var(--popover-foreground);
        --tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-select-dropdown{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-select-option{
        cursor:pointer;
        align-items:center;
        column-gap:calc(var(--spacing)*2.5);
        border-radius:calc(var(--radius) - 2px);
        width:100%;
        padding-inline:calc(var(--spacing)*2.5);
        padding-block:calc(var(--spacing)*2);
        text-align:start;
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        display:flex
    }
    .kt-select-option:disabled{
        pointer-events:none;
        opacity:.7;
        background-color: var(--muted);
    }
    @media (hover:hover){
        .kt-select-option:hover{
            background-color:var(--accent);
            color:var(--accent-foreground)
        }
    }
    .kt-select-option.disabled{
        pointer-events:none;
        opacity:.5
    }
    .kt-select-option.selected,.kt-select-option.highlighted{
        background-color:var(--accent);
        color:var(--accent-foreground)
    }
    .kt-select-group{
        padding-block:calc(var(--spacing)*1)
    }
    .kt-select-group-header{
        padding-inline:calc(var(--spacing)*3);
        padding-block:calc(var(--spacing)*1);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height));
        --tw-font-weight:var(--font-weight-semibold);
        font-weight:var(--font-weight-semibold);
        color:var(--muted-foreground);
        text-transform:uppercase
    }
    .kt-select{
        height:calc(var(--spacing)*8.5);
        gap:calc(var(--spacing)*1);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*3);
        --tw-leading:var(--text-sm--line-height);
        font-size:.8125rem;
        line-height:var(--text-sm--line-height)
    }
    .kt-select-lg{
        height:calc(var(--spacing)*10);
        gap:calc(var(--spacing)*1.5);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*4);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-select-sm{
        height:calc(var(--spacing)*7);
        gap:calc(var(--spacing)*1);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*2.5);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .dark .kt-input[aria-invalid=true]{
        border-color:var(--destructive);
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark .kt-input[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)
        }
    }
    .kt-switch{
        cursor:pointer;
        appearance:none;
        background-color:var(--input);
        flex-shrink:0;
        display:inline-flex;
        position:relative
    }
    .kt-switch:focus-visible{
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--ring);
        --tw-ring-offset-width:2px;
        --tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color);
        --tw-ring-offset-color:var(--background);
        --tw-outline-style:none;
        outline-style:none
    }
    @media (forced-colors:active){
        .kt-switch:focus-visible{
            outline-offset:2px;
            outline:2px solid #0000
        }
    }
    .kt-switch:disabled{
        cursor:not-allowed;
        opacity:.5
    }
    .kt-switch[aria-invalid=true]{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-switch[aria-invalid=true]{
            border-color:color-mix(in oklab,var(--destructive)60%,transparent)
        }
    }
    .kt-switch[aria-invalid=true]{
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-switch[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)10%,transparent)
        }
    }
    .kt-switch:before{
        pointer-events:none;
        --tw-translate-x:calc(var(--spacing)*1);
        translate:var(--tw-translate-x)var(--tw-translate-y);
        --tw-translate-y:calc(calc(1/2*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y);
        background-color:var(--color-white);
        --tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        transition-property:transform,translate,scale,rotate;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        --tw-content:"";
        content:var(--tw-content);
        inset-inline-start:calc(var(--spacing)*0);
        border-radius:3.40282e38px;
        display:block;
        position:absolute;
        top:50%
    }
    .kt-switch:checked,.kt-switch[aria-checked=true]{
        background-color:var(--primary)
    }
    .kt-switch{
        height:calc(var(--spacing)*5);
        width:calc(var(--spacing)*7.5);
        border-radius:3.40282e38px
    }
    .kt-switch:before{
        width:calc(var(--spacing)*3);
        height:calc(var(--spacing)*3)
    }
    :is(.kt-switch:checked,.kt-switch[aria-checked=true]):before{
        --tw-translate-x:calc(var(--spacing)*3.5);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .kt-switch-sm{
        height:calc(var(--spacing)*4.5);
        width:calc(var(--spacing)*6.5);
        border-radius:3.40282e38px
    }
    .kt-switch-sm:before{
        width:calc(var(--spacing)*2.5);
        height:calc(var(--spacing)*2.5)
    }
    :is(.kt-switch-sm:checked,.kt-switch-sm[aria-checked=true]):before{
        --tw-translate-x:calc(var(--spacing)*3);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .kt-switch-lg{
        height:calc(var(--spacing)*5.5);
        width:calc(var(--spacing)*8.5);
        border-radius:3.40282e38px
    }
    .kt-switch-lg:before{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5)
    }
    :is(.kt-switch-lg:checked,.kt-switch-lg[aria-checked=true]):before{
        --tw-translate-x:calc(var(--spacing)*4);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    [dir=rtl] .kt-switch:before{
        --tw-translate-x:calc(var(--spacing)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    :is([dir=rtl] .kt-switch:checked,[dir=rtl] .kt-switch[aria-checked=true]):before{
        --tw-translate-x:calc(var(--spacing)*-3.5);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    :is([dir=rtl] .kt-switch.kt-switch-sm:checked,[dir=rtl] .kt-switch.kt-switch-sm[aria-checked=true]):before{
        --tw-translate-x:calc(var(--spacing)*-3);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    :is([dir=rtl] .kt-switch.kt-switch-lg:checked,[dir=rtl] .kt-switch.kt-switch-lg[aria-checked=true]):before{
        --tw-translate-x:calc(var(--spacing)*-4);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .dark .kt-switch[aria-invalid=true]{
        border-color:var(--destructive);
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark .kt-switch[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)
        }
    }
    .kt-tabs.kt-tabs-line{
        align-items:center;
        gap:calc(var(--spacing)*7);
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border);
        display:flex
    }
    .kt-tabs.kt-tabs-line .kt-tab-toggle{
        cursor:pointer;
        align-items:center;
        gap:calc(var(--spacing)*2);
        border-bottom-style:var(--tw-border-style);
        padding-block:calc(var(--spacing)*2);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        color:var(--secondary-foreground);
        border-bottom-width:2px;
        border-bottom-color:#0000;
        display:inline-flex
    }
    @media (hover:hover){
        .kt-tabs.kt-tabs-line .kt-tab-toggle:hover{
            color:var(--primary)
        }
    }
    .kt-tabs.kt-tabs-line .kt-tab-toggle[data-kt-tab-toggle].active,[data-kt-tab-toggle].active :is(.kt-tabs.kt-tabs-line .kt-tab-toggle),[data-kt-tabs-initialized] [data-kt-dropdown-initialized]:has([data-kt-tab-toggle].active) :is(.kt-tabs.kt-tabs-line .kt-tab-toggle){
        border-color:var(--primary);
        color:var(--primary)
    }
    .kt-tabs.kt-tabs-line .kt-tab-toggle svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5);
        color:var(--muted-foreground);
        flex-shrink:0
    }
    .kt-tabs.kt-tabs-line .kt-tab-toggle svg[data-kt-tab-toggle].active,[data-kt-tab-toggle].active :is(.kt-tabs.kt-tabs-line .kt-tab-toggle svg),[data-kt-tabs-initialized] [data-kt-dropdown-initialized]:has([data-kt-tab-toggle].active) :is(.kt-tabs.kt-tabs-line .kt-tab-toggle svg),.kt-tabs.kt-tabs-line .kt-tab-toggle:hover svg{
        color:var(--primary)
    }
    .kt-textarea{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--input);
        background-color:var(--background);
        width:100%;
        color:var(--foreground);
        --tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d;
        transition-property:color,box-shadow;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration))
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-textarea{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-textarea::placeholder{
        color:var(--muted-foreground)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-textarea::placeholder{
            color:color-mix(in oklab,var(--muted-foreground)80%,transparent)
        }
    }
    .kt-textarea:focus-visible{
        border-color:var(--ring);
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-ring-color:var(--ring)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-textarea:focus-visible{
            --tw-ring-color:color-mix(in oklab,var(--ring)30%,transparent)
        }
    }
    .kt-textarea:focus-visible{
        --tw-outline-style:none;
        outline-style:none
    }
    .kt-textarea:disabled{
        cursor:not-allowed;
        opacity:.7;
        background-color: var(--muted);
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-textarea[aria-invalid=true]{
            border-color:color-mix(in oklab,var(--destructive)60%,transparent)
        }
    }
    .kt-textarea[readonly]{
        opacity:.7
    }
    .kt-textarea[aria-invalid=true]{
        border-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-textarea[aria-invalid=true]{
            border-color:color-mix(in oklab,var(--destructive)60%,transparent)
        }
    }
    .kt-textarea[aria-invalid=true]{
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-textarea[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)10%,transparent)
        }
    }
    .kt-textarea{
        border-radius:calc(var(--radius) - 2px);
        padding:calc(var(--spacing)*3);
        --tw-leading:var(--text-sm--line-height);
        font-size:.8125rem;
        line-height:var(--text-sm--line-height)
    }
    .kt-textarea-lg{
        border-radius:calc(var(--radius) - 2px);
        padding:calc(var(--spacing)*4);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .kt-textarea-sm{
        border-radius:calc(var(--radius) - 2px);
        padding:calc(var(--spacing)*2.5);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .dark .kt-textarea[aria-invalid=true]{
        border-color:var(--destructive);
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark .kt-textarea[aria-invalid=true]{
            --tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)
        }
    }
    .kt-tooltip{
        border-radius:calc(var(--radius) - 2px);
        background-color:var(--mono);
        padding:calc(var(--spacing)*1.5);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height));
        color:var(--mono-foreground);
        --tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-tooltip{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-tooltip:is(.dark *){
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border)
    }
    .kt-tooltip:not(.show){
        display:none
    }
    .kt-tooltip-light{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--popover);
        color:var(--popover-foreground)
    }
    .kt-popover{
        border-radius:calc(var(--radius) - 2px);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--popover);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        color:var(--popover-foreground);
        --tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-popover{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-popover:not(.show){
        display:none
    }
    .kt-popover-header{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border);
        padding-inline:calc(var(--spacing)*2.5);
        padding-block:calc(var(--spacing)*2);
        --tw-font-weight:var(--font-weight-semibold);
        font-weight:var(--font-weight-semibold);
        color:var(--mono)
    }
    .kt-popover-content{
        padding-inline:calc(var(--spacing)*2.5);
        padding-block:calc(var(--spacing)*2)
    }
    .kt-table-wrapper{
        width:100%;
        position:relative;
        overflow:auto
    }
    .kt-table{
        border-collapse:collapse;
        text-align:left;
        vertical-align:bottom;
        width:100%;
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        color:var(--foreground);
        caption-side:bottom
    }
    .kt-table tr{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border);
        transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration))
    }
    .kt-table caption{
        margin-top:calc(var(--spacing)*4);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        color:var(--muted-foreground)
    }
    .kt-table thead tr{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px
    }
    .kt-table thead th{
        height:calc(var(--spacing)*10);
        background-color:var(--muted);
        padding-inline:calc(var(--spacing)*4);
        text-align:left;
        vertical-align:middle;
        --tw-font-weight:var(--font-weight-normal);
        font-weight:var(--font-weight-normal);
        color:var(--secondary-foreground)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-table thead th{
            background-color:color-mix(in oklab,var(--muted)40%,transparent)
        }
    }
    .kt-table thead th:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        text-align:right
    }
    .kt-table thead th:has([role=checkbox]){
        padding-inline-end:calc(var(--spacing)*0)
    }
    .kt-table tbody tr:last-child{
        border-style:var(--tw-border-style);
        border-width:0
    }
    .kt-table tbody td{
        padding-inline:calc(var(--spacing)*4);
        padding-block:calc(var(--spacing)*3);
        vertical-align:middle
    }
    .kt-table tbody td:has([role=checkbox]){
        padding-inline-end:calc(var(--spacing)*0)
    }
    .kt-table tfoot{
        border-top-style:var(--tw-border-style);
        background-color:var(--muted);
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        border-top-width:1px
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-table tfoot{
            background-color:color-mix(in oklab,var(--muted)50%,transparent)
        }
    }
    .kt-table tfoot:last-child>tr{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:0
    }
    .kt-table tfoot th{
        height:calc(var(--spacing)*10);
        padding-inline:calc(var(--spacing)*4);
        text-align:left;
        vertical-align:middle;
        --tw-font-weight:var(--font-weight-normal);
        font-weight:var(--font-weight-normal);
        color:var(--secondary-foreground)
    }
    .kt-table tfoot th:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        text-align:right
    }
    .kt-table tfoot th:has([role=checkbox]){
        padding-inline-end:calc(var(--spacing)*0)
    }
    :is(.kt-table td,.kt-table th) input[type=checkbox]{
        vertical-align:inherit
    }
    :is([data-kt-datatable-table],.kt-table-highlight) tr.checked,:is([data-kt-datatable-table],.kt-table-highlight) tr:has(td):hover{
        background-color:var(--muted)
    }
    @supports (color:color-mix(in lab, red, red)){
        :is([data-kt-datatable-table],.kt-table-highlight) tr:has(td):hover{
            background-color:color-mix(in oklab,var(--muted)50%,transparent)
        }
    }
    .kt-table-col{
        cursor:pointer;
        align-items:center;
        gap:.35rem;
        display:inline-flex
    }
    .kt-table-col-label{
        white-space:nowrap;
        flex-wrap:nowrap;
        align-items:center;
        gap:.35rem;
        display:inline-flex
    }
    .kt-table-col-sort{
        flex-direction:column;
        justify-content:center;
        align-items:center;
        gap:.125rem;
        width:.975rem;
        height:.975rem;
        line-height:1;
        display:inline-flex
    }
    .kt-table-col-sort:before{
        content:"";
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M1.08333 4.83333C0.908333 4.83333 0.791667 4.775 0.675 4.65833C0.441667 4.425 0.441667 4.075 0.675 3.84167L3.59167 0.925C3.825 0.691667 4.175 0.691667 4.40833 0.925L7.325 3.84167C7.55833 4.075 7.55833 4.425 7.325 4.65833C7.09167 4.89167 6.74167 4.89167 6.50833 4.65833L4 2.15L1.49167 4.65833C1.375 4.775 1.25833 4.83333 1.08333 4.83333Z' fill='%2378829D'/%3E%3C/svg%3E");
        background-position:50%;
        background-repeat:no-repeat;
        background-size:cover;
        width:.438rem;
        height:.25rem;
        display:inline-block
    }
    .kt-table-col-sort:after{
        content:"";
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M4 4.24984C3.825 4.24984 3.70833 4.1915 3.59167 4.07484L0.675 1.15817C0.441667 0.924838 0.441667 0.574837 0.675 0.341504C0.908333 0.108171 1.25833 0.108171 1.49167 0.341504L4 2.84984L6.50833 0.341504C6.74167 0.108171 7.09167 0.108171 7.325 0.341504C7.55833 0.574837 7.55833 0.924838 7.325 1.15817L4.40833 4.07484C4.29167 4.1915 4.175 4.24984 4 4.24984Z' fill='%2378829D'/%3E%3C/svg%3E");
        background-position:50%;
        background-repeat:no-repeat;
        background-size:cover;
        width:.438rem;
        height:.25rem;
        display:inline-block
    }
    [aria-sort=asc] .kt-table-col-sort:before{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M1.08333 4.83333C0.908333 4.83333 0.791667 4.775 0.675 4.65833C0.441667 4.425 0.441667 4.075 0.675 3.84167L3.59167 0.925C3.825 0.691667 4.175 0.691667 4.40833 0.925L7.325 3.84167C7.55833 4.075 7.55833 4.425 7.325 4.65833C7.09167 4.89167 6.74167 4.89167 6.50833 4.65833L4 2.15L1.49167 4.65833C1.375 4.775 1.25833 4.83333 1.08333 4.83333Z' fill='%234B5675'/%3E%3C/svg%3E")
    }
    [aria-sort=asc] .kt-table-col-sort:after{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M4 4.24984C3.825 4.24984 3.70833 4.1915 3.59167 4.07484L0.675 1.15817C0.441667 0.924838 0.441667 0.574837 0.675 0.341504C0.908333 0.108171 1.25833 0.108171 1.49167 0.341504L4 2.84984L6.50833 0.341504C6.74167 0.108171 7.09167 0.108171 7.325 0.341504C7.55833 0.574837 7.55833 0.924838 7.325 1.15817L4.40833 4.07484C4.29167 4.1915 4.175 4.24984 4 4.24984Z' fill='%23C9CEDA'/%3E%3C/svg%3E")
    }
    [aria-sort=desc] .kt-table-col-sort:before{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M1.08333 4.83333C0.908333 4.83333 0.791667 4.775 0.675 4.65833C0.441667 4.425 0.441667 4.075 0.675 3.84167L3.59167 0.925C3.825 0.691667 4.175 0.691667 4.40833 0.925L7.325 3.84167C7.55833 4.075 7.55833 4.425 7.325 4.65833C7.09167 4.89167 6.74167 4.89167 6.50833 4.65833L4 2.15L1.49167 4.65833C1.375 4.775 1.25833 4.83333 1.08333 4.83333Z' fill='%23C9CEDA'/%3E%3C/svg%3E")
    }
    [aria-sort=desc] .kt-table-col-sort:after{
        background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M4 4.24984C3.825 4.24984 3.70833 4.1915 3.59167 4.07484L0.675 1.15817C0.441667 0.924838 0.441667 0.574837 0.675 0.341504C0.908333 0.108171 1.25833 0.108171 1.49167 0.341504L4 2.84984L6.50833 0.341504C6.74167 0.108171 7.09167 0.108171 7.325 0.341504C7.55833 0.574837 7.55833 0.924838 7.325 1.15817L4.40833 4.07484C4.29167 4.1915 4.175 4.24984 4 4.24984Z' fill='%234B5675'/%3E%3C/svg%3E")
    }
    .kt-table-border{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border)
    }
    .kt-table-border td,.kt-table-border th{
        border-inline-end-style:var(--tw-border-style);
        border-inline-end-width:1px;
        border-color:var(--border)
    }
    :is(.kt-table-border td,.kt-table-border th):last-child{
        border-inline-end-style:var(--tw-border-style);
        border-inline-end-width:0
    }
    .kt-table-border-s{
        border-inline-start-style:var(--tw-border-style);
        border-inline-start-width:1px;
        border-color:var(--border)
    }
    .kt-table-border-e{
        border-inline-end-style:var(--tw-border-style);
        border-inline-end-width:1px;
        border-color:var(--border)
    }
    .kt-table-border-t{
        border-top-style:var(--tw-border-style);
        border-top-width:1px;
        border-color:var(--border)
    }
    .kt-table-border-b{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border)
    }
    [dir=rtl] .kt-table{
        text-align:right
    }
    .kt-toggle-group{
        background-color:var(--background);
        --tw-leading:1;
        align-items:center;
        line-height:1;
        display:inline-flex
    }
    .kt-toggle-group .kt-btn{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-inline-end-style:var(--tw-border-style);
        border-inline-end-width:0;
        border-color:var(--border);
        color:var(--accent-foreground);
        background-color:#0000;
        flex-grow:1
    }
    .kt-toggle-group .kt-btn:last-child{
        border-inline-end-style:var(--tw-border-style);
        border-inline-end-width:1px;
        border-color:var(--border)
    }
    .kt-toggle-group .kt-btn:not(:first-child){
        border-start-start-radius:0;
        border-end-start-radius:0
    }
    .kt-toggle-group .kt-btn:not(:last-child){
        border-start-end-radius:0;
        border-end-end-radius:0
    }
    .kt-toggle-group .kt-btn:not(:first-child):not(:last-child){
        border-radius:0
    }
    .kt-toggle-group .kt-btn svg,.kt-toggle-group .kt-btn i{
        color:var(--muted-foreground)
    }
    .kt-toggle-group .kt-btn input[type=checkbox],.kt-toggle-group .kt-btn input[type=radio]{
        display:none
    }
    .kt-toggle-group .kt-btn:hover,.kt-toggle-group .kt-btn:focus,.kt-toggle-group .kt-btn:active,.kt-toggle-group .kt-btn:has(input:checked),.kt-toggle-group .kt-btn.active{
        background-color:var(--accent);
        color:var(--accent-foreground)
    }
    :is(.kt-toggle-group .kt-btn:hover,.kt-toggle-group .kt-btn:focus,.kt-toggle-group .kt-btn:active,.kt-toggle-group .kt-btn:has(input:checked),.kt-toggle-group .kt-btn.active) svg,:is(.kt-toggle-group .kt-btn:hover,.kt-toggle-group .kt-btn:focus,.kt-toggle-group .kt-btn:active,.kt-toggle-group .kt-btn:has(input:checked),.kt-toggle-group .kt-btn.active) i{
        color:var(--accent-foreground)
    }
    .kt-separator{
        background-color:var(--border);
        flex-shrink:0;
        width:100%;
        height:1px
    }
    .kt-separator-vertical{
        width:1px;
        height:100%
    }
    .kt-progress{
        height:calc(var(--spacing)*1);
        background-color:var(--secondary);
        border-radius:3.40282e38px;
        width:100%;
        position:relative;
        overflow:hidden
    }
    .kt-progress-indicator{
        width:100%;
        height:100%;
        transform:translateX:(calc(100 - var(--progress-value)));
        background-color:var(--primary);
        transition-property:all;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration));
        border-radius:3.40282e38px;
        flex:1
    }
    .kt-progress-primary .kt-progress-indicator{
        background-color:var(--primary)
    }
    .kt-progress-success .kt-progress-indicator{
        background-color:var(--color-green-500)
    }
    .kt-progress-warning .kt-progress-indicator{
        background-color:var(--color-yellow-500)
    }
    .kt-progress-info .kt-progress-indicator{
        background-color:var(--color-violet-500)
    }
    .kt-progress-destructive .kt-progress-indicator{
        background-color:var(--destructive)
    }
    .kt-progress-mono .kt-progress-indicator{
        background-color:var(--mono)
    }
    .kt-pagination{
        align-items:center;
        gap:calc(var(--spacing)*1);
        display:flex
    }
    .kt-pagination-item{
        align-items:center;
        gap:calc(var(--spacing)*1);
        flex-direction:row;
        flex-shrink:0;
        display:flex
    }
    .kt-pagination-item svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground)
    }
    .kt-pagination-ellipsis{
        height:calc(var(--spacing)*9);
        width:calc(var(--spacing)*9);
        justify-content:center;
        align-items:center;
        display:flex
    }
    .kt-pagination-ellipsis svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4);
        color:var(--muted-foreground)
    }
    .kt-skeleton{
        animation:var(--animate-pulse);
        border-radius:calc(var(--radius) - 2px);
        background-color:var(--accent)
    }
    .kt-kbd{
        border-radius:calc(var(--radius) - 2px);
        font-family:var(--font-mono);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--accent);
        color:var(--accent-foreground);
        justify-content:center;
        align-items:center;
        display:inline-flex
    }
    .kt-kbd-outline{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--input);
        color:var(--accent-foreground);
        background-color:#0000
    }
    .kt-kbd{
        height:calc(var(--spacing)*7);
        min-width:calc(var(--spacing)*7);
        padding-inline:calc(var(--spacing)*1.5);
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .kt-kbd svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5)
    }
    .kt-kbd-xs{
        height:calc(var(--spacing)*5);
        min-width:calc(var(--spacing)*5);
        padding-inline:calc(var(--spacing)*1);
        --tw-leading:.75rem;
        font-size:.6875rem;
        line-height:.75rem
    }
    .kt-kbd-xs svg{
        width:calc(var(--spacing)*3);
        height:calc(var(--spacing)*3)
    }
    .kt-kbd-sm{
        height:calc(var(--spacing)*6);
        min-width:calc(var(--spacing)*6);
        padding-inline:calc(var(--spacing)*1);
        --tw-leading:.75rem;
        font-size:.75rem;
        line-height:.75rem
    }
    .kt-kbd-sm svg{
        width:calc(var(--spacing)*3);
        height:calc(var(--spacing)*3)
    }
    .kt-breadcrumb{
        align-items:center;
        gap:calc(var(--spacing)*1.5);
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        overflow-wrap:break-word;
        color:var(--muted-foreground);
        flex-wrap:wrap;
        display:flex
    }
    .kt-breadcrumb-item{
        align-items:center;
        gap:calc(var(--spacing)*1.5);
        display:inline-flex
    }
    .kt-breadcrumb-link{
        transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration))
    }
    @media (hover:hover){
        .kt-breadcrumb-link:hover{
            color:var(--foreground)
        }
    }
    .kt-breadcrumb-link svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4)
    }
    .kt-breadcrumb-page{
        --tw-font-weight:var(--font-weight-normal);
        font-weight:var(--font-weight-normal);
        color:var(--foreground)
    }
    .kt-breadcrumb-separator svg{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5)
    }
    .kt-breadcrumb-ellipsis svg{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4)
    }
    .apexcharts-text,.apexcharts-title-text,.apexcharts-legend-text{
        font-family:inherit!important
    }
    .apexcharts-title-text{
        font-weight:var(--font-weight-normal)
    }
    .apexcharts-pie-label{
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .apexcharts-toolbar{
        text-align:start!important
    }
    .apexcharts-menu{
        overflow:hidden;
        border:1px solid var(--color-border)!important;
        box-shadow:var(--box-shadow-sm)!important;
        background-color:var(--color-background)!important;
        border-radius:.625rem!important;
        min-width:10rem!important;
        padding:.5rem 0!important
    }
    .apexcharts-menu .apexcharts-menu-item{
        padding:.5rem!important
    }
    .apexcharts-menu .apexcharts-menu-item:hover{
        background-color:var(--gray-100)!important
    }
    .apexcharts-tooltip{
        border:1px solid var(--color-border)!important;
        box-shadow:var(--box-shadow-sm)!important;
        background-color:var(--color-background)!important;
        color:var(--color-secondary-foreground)!important;
        border-radius:.625rem!important
    }
    .apexcharts-tooltip .apexcharts-tooltip-title{
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        color:var(--foreground);
        border-bottom:1px solid var(--color-border)!important;
        background-color:#0000!important;
        padding:.25rem .5rem!important
    }
    .apexcharts-xaxistooltip{
        color:var(--color-mono);
        border-radius:.625rem;
        border:1px solid var(--color-border)!important;
        box-shadow:var(--box-shadow-sm)!important;
        background-color:var(--color-background)!important
    }
    .apexcharts-xaxistooltip:before{
        border-bottom:0!important
    }
    .apexcharts-legend{
        gap:calc(var(--spacing)*2);
        flex-direction:column;
        display:flex
    }
    .apexcharts-legend .apexcharts-legend-series{
        gap:calc(var(--spacing)*1);
        align-items:center;
        display:flex
    }
    .apexcharts-legend .apexcharts-legend-series .apexcharts-legend-text{
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium);
        color:var(--muted-foreground)
    }
    .apexcharts-card-rounded .apexcharts-canvas svg{
        border-bottom-left-radius:var(--radius-xl);
        border-bottom-right-radius:var(--radius-xl)
    }
    .apexcharts-rounded-sm .apexcharts-canvas svg{
        border-radius:var(--radius-sm)
    }
    .apexcharts-rounded .apexcharts-canvas svg{
        border-radius:var(--radius-md)
    }
    .apexcharts-rounded-lg .apexcharts-canvas svg{
        border-radius:var(--radius-lg)
    }
    .apexcharts-rounded-xl .apexcharts-canvas svg{
        border-radius:var(--radius-xl)
    }
    .kt-container-fixed{
        width:100%;
        padding-inline:calc(var(--spacing)*6);
        flex-grow:1
    }
    @media (min-width:80rem){
        .kt-container-fixed{
            /* max-width:var(--breakpoint-xl); */
            padding-inline:calc(var(--spacing)*7.5);
            margin-inline:auto
        }
    }
    .kt-container-fluid{
        width:100%;
        padding-inline:calc(var(--spacing)*6);
        flex-grow:1
    }
    @media (min-width:80rem){
        .kt-container-fluid{
            padding-inline:calc(var(--spacing)*7.5)
        }
    }
    .leaflet-container .leaflet-pane,.leaflet-container .leaflet-top,.leaflet-container .leaflet-bottom,.leaflet-container .leaflet-control{
        z-index:1!important
    }
    .leaflet-container .leaflet-popup-content-wrapper{
        border-radius:var(--radius-md);
        text-align:center;
        background-color:var(--color-popover)
    }
    .leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content{
        font-family:inherit;
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .kt-menu{
        display:flex
    }
    .kt-menu-item,.kt-menu-link{
        margin:0;
        padding:0
    }
    .kt-menu-item{
        flex-direction:column;
        display:flex
    }
    .kt-menu-link,.kt-menu-label,.kt-menu-toggle{
        cursor:pointer;
        flex-grow:1;
        align-items:center;
        display:flex
    }
    .kt-menu-title{
        flex-grow:1;
        align-items:center;
        line-height:1;
        display:flex
    }
    .kt-menu-icon,.kt-menu-bullet,.kt-menu-badge,.kt-menu-arrow{
        flex-shrink:0;
        align-items:center;
        display:flex
    }
    .kt-menu-dropdown,.kt-menu-accordion{
        flex-direction:column;
        align-items:stretch;
        margin:0;
        padding:0;
        display:none
    }
    .show.kt-menu-item-dropdown>.kt-menu-dropdown,.base-popper-root>.kt-menu-container>.kt-menu-dropdown,.kt-menu-dropdown.kt-menu.show,.kt-menu-dropdown.show[data-popper-placement]{
        will-change:transform;
        display:flex
    }
    .kt-menu-accordion{
        transition:height .3s;
        display:none
    }
    .show:not(.kt-menu-dropdown)>.kt-menu-accordion,.transitioning:not(.kt-menu-dropdown)>.kt-menu-accordion,.kt-menu-accordion.show{
        display:flex
    }
    .kt-menu-dropdown{
        gap:calc(var(--spacing)*.25);
        border-radius:calc(var(--radius) - 2px);
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--popover);
        color:var(--popover-foreground);
        --tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        --tw-shadow-color:#0000000d
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-menu-dropdown{
            --tw-shadow-color:color-mix(in oklab,#0000000d var(--tw-shadow-alpha),transparent)
        }
    }
    .kt-menu-default{
        gap:calc(var(--spacing)*.5);
        padding-block:calc(var(--spacing)*2)
    }
    .kt-menu-default .kt-menu-link,.kt-menu-default .kt-menu-label{
        margin-inline:calc(var(--spacing)*2);
        border-radius:calc(var(--radius) - 2px);
        padding-inline:calc(var(--spacing)*2);
        padding-block:calc(var(--spacing)*2)
    }
    .kt-menu-default .kt-menu-title{
        font-size:var(--text-2sm);
        line-height:var(--tw-leading,var(--text-2sm--line-height));
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium)
    }
    .kt-menu-default .kt-menu-icon{
        margin-inline-end:calc(var(--spacing)*2.5)
    }
    .kt-menu-default .kt-menu-icon i{
        font-size:var(--text-lg);
        line-height:var(--tw-leading,var(--text-lg--line-height))
    }
    .kt-menu-default .kt-menu-bullet{
        margin-inline-end:calc(var(--spacing)*2.5)
    }
    .kt-menu-default .kt-menu-arrow{
        margin-inline-start:calc(var(--spacing)*2)
    }
    .kt-menu-default .kt-menu-arrow i{
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .kt-menu-default .kt-menu-badge{
        margin-inline-start:calc(var(--spacing)*2.5)
    }
    .kt-menu-default .kt-menu-separator{
        margin-block:calc(var(--spacing)*2);
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px;
        border-color:var(--border)
    }
    .kt-menu-default .kt-menu-accordion:not(.kt-menu-no-indent) .kt-menu-item>.kt-menu-link,.kt-menu-default .kt-menu-accordion:not(.kt-menu-no-indent) .kt-menu-item>.kt-menu-label{
        margin-inline-start:calc(var(--spacing)*5)
    }
    .kt-menu-default .kt-menu-accordion:not(.kt-menu-no-indent) .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-link,.kt-menu-default .kt-menu-accordion:not(.kt-menu-no-indent) .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-label{
        margin-inline-start:calc(var(--spacing)*8)
    }
    .kt-menu-default .kt-menu-accordion:not(.kt-menu-no-indent) .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-link,.kt-menu-default .kt-menu-accordion:not(.kt-menu-no-indent) .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-label{
        margin-inline-start:calc(var(--spacing)*11)
    }
    .kt-menu-default .kt-menu-accordion:not(.kt-menu-no-indent) .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-link,.kt-menu-default .kt-menu-accordion:not(.kt-menu-no-indent) .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-accordion .kt-menu-item>.kt-menu-label{
        margin-inline-start:calc(var(--spacing)*14)
    }
    .kt-menu-fit{
        padding-block:calc(var(--spacing)*0)
    }
    .kt-menu-fit .kt-menu-link,.kt-menu-fit .kt-menu-label{
        margin-inline:calc(var(--spacing)*0)
    }
    .kt-menu-space{
        padding-block:calc(var(--spacing)*2)
    }
    .kt-menu-space .kt-menu-link,.kt-menu-space .kt-menu-label{
        margin-inline:calc(var(--spacing)*2.5)
    }
    .kt-menu-default .kt-menu-item .kt-menu-title{
        color:var(--foreground)
    }
    .kt-menu-default .kt-menu-item .kt-menu-icon i,.kt-menu-default .kt-menu-item .kt-menu-arrow i{
        color:var(--muted-foreground)
    }
    :is(.kt-menu-default .kt-menu-item .kt-menu-link:hover,.kt-menu-default .kt-menu-item .kt-menu-label:hover) .kt-menu-title{
        color:var(--mono)
    }
    :is(.kt-menu-default .kt-menu-item .kt-menu-link:hover,.kt-menu-default .kt-menu-item .kt-menu-label:hover) .kt-menu-icon i{
        color:var(--primary)
    }
    :is(:is(.kt-menu-default .kt-menu-item.active,.kt-menu-default .kt-menu-item.show,.kt-menu-default .kt-menu-item.here,.kt-menu-default .kt-menu-item.focus)>.kt-menu-link,:is(.kt-menu-default .kt-menu-item.active,.kt-menu-default .kt-menu-item.show,.kt-menu-default .kt-menu-item.here,.kt-menu-default .kt-menu-item.focus)>.kt-menu-label) .kt-menu-title{
        color:var(--mono)
    }
    :is(:is(.kt-menu-default .kt-menu-item.active,.kt-menu-default .kt-menu-item.show,.kt-menu-default .kt-menu-item.here,.kt-menu-default .kt-menu-item.focus)>.kt-menu-link,:is(.kt-menu-default .kt-menu-item.active,.kt-menu-default .kt-menu-item.show,.kt-menu-default .kt-menu-item.here,.kt-menu-default .kt-menu-item.focus)>.kt-menu-label) .kt-menu-icon i{
        color:var(--primary)
    }
    :is(.kt-menu-default .kt-menu-item.active,.kt-menu-default .kt-menu-item.here)>.kt-menu-link,:is(.kt-menu-default .kt-menu-item.active,.kt-menu-default .kt-menu-item.here)>.kt-menu-label,.kt-menu-default .kt-menu-item>.kt-menu-link:hover,.kt-menu-default .kt-menu-item>.kt-menu-label:hover{
        background-color:var(--accent)
    }
    .kt-menu-default .kt-menu-item.disabled>.kt-menu-link,.kt-menu-default .kt-menu-item.disabled>.kt-menu-label{
        opacity:.5
    }
    .range{
        appearance:none;
        width:100%;
        height:.5rem;
        padding:0
    }
    .range:focus{
        outline:0
    }
    .range::-moz-focus-outer{
        border:0
    }
    .range::-webkit-slider-thumb{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--primary);
        --tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        appearance:none;
        border-radius:3.40282e38px;
        width:1.5rem;
        height:1.5rem;
        margin-top:-.5rem;
        transition:background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out
    }
    .range::-webkit-slider-thumb:active{
        background-color:var(--primary)
    }
    .range::-moz-range-thumb{
        border-style:var(--tw-border-style);
        border-width:1px;
        border-color:var(--border);
        background-color:var(--primary);
        --tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
        appearance:none;
        border-radius:3.40282e38px;
        width:1.5rem;
        height:1.5rem;
        margin-top:-.5rem;
        transition:background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out
    }
    .range::-moz-range-thumb:active{
        background-color:var(--primary)
    }
    .range::-moz-range-track{
        background-color:var(--input);
        color:#0000;
        cursor:pointer;
        border-color:#0000;
        border-radius:.188rem;
        width:100%;
        height:.5rem
    }
    .range::-webkit-slider-runnable-track{
        background-color:var(--input);
        color:#0000;
        cursor:pointer;
        border-color:#0000;
        border-radius:.188rem;
        width:100%;
        height:.5rem
    }
    .range:disabled{
        pointer-events:none
    }
    .range:disabled::-webkit-slider-thumb{
        background-color:var(--accent)
    }
    .range:disabled::-moz-range-thumb{
        background-color:var(--accent)
    }
    .kt-rating{
        align-items:stretch;
        display:inline-flex
    }
    .kt-rating input{
        appearance:none;
        position:absolute;
        inset-inline-start:9999px
    }
    .kt-rating input[disabled]{
        display:none
    }
    .kt-rating-on{
        color:var(--color-yellow-400)
    }
    .kt-rating-off{
        color:var(--muted-foreground)
    }
    .kt-rating-label{
        align-items:center;
        display:inline-flex!important
    }
    .kt-rating-label .kt-rating-on{
        display:none!important
    }
    .kt-rating-label .kt-rating-off,:is(.kt-rating:hover label.kt-rating-label,label.kt-rating-label,label.kt-rating-label.checked,div.kt-rating-label.checked) .kt-rating-on{
        display:inline-flex!important
    }
    :is(.kt-rating:hover label.kt-rating-label,label.kt-rating-label,label.kt-rating-label.checked,div.kt-rating-label.checked) .kt-rating-off,:is(label.kt-rating-label:hover~label.kt-rating-label,.kt-rating-input:checked~.kt-rating-label) .kt-rating-on{
        display:none!important
    }
    :is(label.kt-rating-label:hover~label.kt-rating-label,.kt-rating-input:checked~.kt-rating-label) .kt-rating-off{
        display:inline-flex!important
    }
    .kt-rating-label.indeterminate{
        position:relative
    }
    .kt-rating-label.indeterminate .kt-rating-on{
        z-index:1;
        position:absolute;
        inset-inline-start:0;
        overflow:hidden;
        display:inline-flex!important
    }
    .kt-rating-label.indeterminate .kt-rating-off{
        display:inline-flex!important
    }
    label.kt-rating-label{
        cursor:pointer
    }
    .kt-scrollable,.kt-scrollable-y,.kt-scrollable-x,.kt-scrollable-hover,.kt-scrollable-y-hover,.kt-scrollable-x-hover,.kt-scrollable-auto,.kt-scrollable-y-auto,.kt-scrollable-x-auto{
        scrollbar-width:thin;
        scrollbar-color:transparent transparent;
        position:relative
    }
    :is(.kt-scrollable,.kt-scrollable-y,.kt-scrollable-x,.kt-scrollable-hover,.kt-scrollable-y-hover,.kt-scrollable-x-hover,.kt-scrollable-auto,.kt-scrollable-y-auto,.kt-scrollable-x-auto)::-webkit-scrollbar{
        width:.35rem;
        height:.35rem
    }
    :is(.kt-scrollable,.kt-scrollable-y,.kt-scrollable-x,.kt-scrollable-hover,.kt-scrollable-y-hover,.kt-scrollable-x-hover,.kt-scrollable-auto,.kt-scrollable-y-auto,.kt-scrollable-x-auto)::-webkit-scrollbar-track{
        background-color:#0000
    }
    :is(.kt-scrollable,.kt-scrollable-y,.kt-scrollable-x,.kt-scrollable-hover,.kt-scrollable-y-hover,.kt-scrollable-x-hover,.kt-scrollable-auto,.kt-scrollable-y-auto,.kt-scrollable-x-auto)::-webkit-scrollbar-thumb{
        border-radius:1.25rem
    }
    :is(.kt-scrollable,.kt-scrollable-y,.kt-scrollable-x,.kt-scrollable-hover,.kt-scrollable-y-hover,.kt-scrollable-x-hover,.kt-scrollable-auto,.kt-scrollable-y-auto,.kt-scrollable-x-auto)::-webkit-scrollbar-corner{
        background-color:#0000
    }
    .kt-scrollable,.kt-scrollable-hover{
        overflow:scroll
    }
    .kt-scrollable-y,.kt-scrollable-y-hover{
        overflow-y:scroll
    }
    .kt-scrollable-x,.kt-scrollable-x-hover{
        overflow-x:scroll
    }
    .kt-scrollable-auto{
        overflow:auto
    }
    .kt-scrollable-y-auto{
        overflow-y:auto
    }
    .kt-scrollable-x-auto{
        overflow-x:auto
    }
    .kt-scrollable,.kt-scrollable-y,.kt-scrollable-x,.kt-scrollable-auto,.kt-scrollable-y-auto,.kt-scrollable-x-auto,.kt-scrollable-hover:hover,.kt-scrollable-y-hover:hover,.kt-scrollable-x-hover:hover{
        scrollbar-color:var(--scrollbar-thumb-color,var(--color-input))transparent
    }
    :is(.kt-scrollable,.kt-scrollable-y,.kt-scrollable-x,.kt-scrollable-auto,.kt-scrollable-y-auto,.kt-scrollable-x-auto,.kt-scrollable-hover:hover,.kt-scrollable-y-hover:hover,.kt-scrollable-x-hover:hover)::-webkit-scrollbar-thumb{
        background-color:var(--scrollbar-thumb-color,var(--color-input))
    }
    :is(.kt-scrollable,.kt-scrollable-y,.kt-scrollable-x,.kt-scrollable-auto,.kt-scrollable-y-auto,.kt-scrollable-x-auto,.kt-scrollable-hover:hover,.kt-scrollable-y-hover:hover,.kt-scrollable-x-hover:hover)::-webkit-scrollbar-corner{
        background-color:#0000
    }
    .demo1{
        --sidebar-transition-duration:.3s;
        --sidebar-transition-timing:ease;
        --sidebar-width:280px;
        --sidebar-width-collapse:80px;
        --sidebar-default-width:280px;
        --header-height:70px
    }
    @media (max-width:64rem){
        .demo1{
            --sidebar-width:280px;
            --header-height:60px
        }
    }
    .demo1 .kt-header{
        height:var(--header-height)
    }
    .demo1 .kt-sidebar{
        width:var(--sidebar-width)
    }
    .demo1.kt-header-fixed .kt-wrapper{
        padding-top:var(--header-height)
    }
    @media (min-width:64rem){
        .demo1 .kt-sidebar{
            width:var(--sidebar-width);
            transition:width var(--sidebar-transition-duration)var(--sidebar-transition-timing)
        }
        .demo1 .kt-sidebar .kt-sidebar-header{
            height:var(--header-height)
        }
        .demo1 .kt-sidebar .kt-sidebar-wrapper,.demo1 .kt-sidebar .kt-sidebar-logo{
            width:var(--sidebar-default-width)
        }
        .demo1 .kt-sidebar .small-logo{
            display:none
        }
        .demo1.kt-sidebar-fixed .kt-wrapper{
            padding-inline-start:var(--sidebar-width)!important
        }
        .demo1.kt-sidebar-fixed.kt-header-fixed .kt-header{
            inset-inline-start:var(--sidebar-width)!important
        }
        .demo1.kt-sidebar-fixed.kt-header-fixed .kt-wrapper{
            padding-top:var(--header-height)
        }
        .demo1.kt-sidebar-collapse{
            --sidebar-width:var(--sidebar-width-collapse)
        }
        .demo1.kt-sidebar-collapse .kt-sidebar{
            transition:width var(--sidebar-transition-duration)var(--sidebar-transition-timing)
        }
        .demo1.kt-sidebar-collapse .kt-sidebar.animating{
            pointer-events:none
        }
        .demo1.kt-sidebar-collapse .kt-sidebar:hover:not(.animating){
            width:var(--sidebar-default-width);
            transition:width var(--sidebar-transition-duration)var(--sidebar-transition-timing)
        }
        .demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .default-logo{
            display:none
        }
        .demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .small-logo{
            display:flex
        }
        .demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .kt-menu>.kt-menu-item>.kt-menu-link .kt-menu-title,.demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .kt-menu>.kt-menu-item>.kt-menu-link .kt-menu-arrow,.demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .kt-menu>.kt-menu-item>.kt-menu-link .kt-menu-badge,.demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .kt-menu>.kt-menu-item>.kt-menu-label .kt-menu-title,.demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .kt-menu>.kt-menu-item>.kt-menu-label .kt-menu-arrow,.demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .kt-menu>.kt-menu-item>.kt-menu-label .kt-menu-badge,.demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .kt-menu>.kt-menu-item>.kt-menu-accordion{
            display:none
        }
        .demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .kt-menu>.kt-menu-item>.kt-menu-heading{
            visibility:hidden;
            position:relative
        }
        .demo1.kt-sidebar-collapse .kt-sidebar:not(:hover) .kt-menu>.kt-menu-item>.kt-menu-heading:before{
            content:"...";
            color:currentColor;
            font-size:inherit;
            visibility:visible;
            bottom:50%;
            margin-inline-start:.225rem;
            display:inline-block;
            position:absolute;
            inset-inline-start:0;
            transform:translate(100%)
        }
        .demo1.kt-sidebar-collapse .kt-sidebar .kt-sidebar-content{
            overflow:hidden
        }
    }
    .demo1 .kt-wrapper{
        transition:padding-inline-start var(--sidebar-transition-duration)var(--sidebar-transition-timing)
    }
    .demo1 .kt-header{
        transition:inset-inline-start var(--sidebar-transition-duration)var(--sidebar-transition-timing)
    }
}
@layer utilities{
    .collapse{
        visibility:collapse
    }
    .invisible{
        visibility:hidden
    }
    .visible{
        visibility:visible
    }
    .sr-only{
        clip:rect(0,0,0,0);
        white-space:nowrap;
        border-width:0;
        width:1px;
        height:1px;
        margin:-1px;
        padding:0;
        position:absolute;
        overflow:hidden
    }
    .absolute{
        position:absolute
    }
    .dropdown-absolute{
        position:absolute!important
    }
    .fixed{
        position:fixed
    }
    .relative{
        position:relative
    }
    .static{
        position:static
    }
    .sticky{
        position:sticky
    }
    .-start-\[3px\]{
        inset-inline-start:-3px
    }
    .start-\(--sidebar-width\){
        inset-inline-start:var(--sidebar-width)
    }
    .start-0{
        inset-inline-start:calc(var(--spacing)*0)
    }
    .start-1\/2,.start-2\/4{
        inset-inline-start:50%
    }
    .start-16{
        inset-inline-start:calc(var(--spacing)*16)
    }
    .start-\[93px\]{
        inset-inline-start:93px
    }
    .start-full{
        inset-inline-start:100%
    }
    .-end-0\.5{
        inset-inline-end:calc(var(--spacing)*-.5)
    }
    .-end-1{
        inset-inline-end:calc(var(--spacing)*-1)
    }
    .-end-2{
        inset-inline-end:calc(var(--spacing)*-2)
    }
    .end-0{
        inset-inline-end:calc(var(--spacing)*0)
    }
    .end-3{
        inset-inline-end:calc(var(--spacing)*3)
    }
    .end-4{
        inset-inline-end:calc(var(--spacing)*4)
    }
    .end-5{
        inset-inline-end:calc(var(--spacing)*5)
    }
    .-top-0\.5{
        top:calc(var(--spacing)*-.5)
    }
    .-top-1{
        top:calc(var(--spacing)*-1)
    }
    .top-\(--header-height\){
        top:var(--header-height)
    }
    .top-0{
        top:calc(var(--spacing)*0)
    }
    .top-1\/2{
        top:50%
    }
    .top-2{
        top:calc(var(--spacing)*2)
    }
    .top-2\/4{
        top:50%
    }
    .top-4{
        top:calc(var(--spacing)*4)
    }
    .top-5{
        top:calc(var(--spacing)*5)
    }
    .top-9{
        top:calc(var(--spacing)*9)
    }
    .top-\[1\.5rem\]{
        top:1.5rem
    }
    .top-\[3rem\]{
        top:3rem
    }
    .top-\[15\%\]{
        top:15%
    }
    .top-\[15px\]{
        top:15px
    }
    .top-\[17px\]{
        top:17px
    }
    .top-\[calc\(var\(--header-height\)\+1\.5rem\)\]{
        top:calc(var(--header-height) + 1.5rem)
    }
    .top-\[calc\(var\(--header-height\)\+1rem\)\]{
        top:calc(var(--header-height) + 1rem)
    }
    .top-\[calc\(var\(--header-height\)\+var\(--navbar-height\)\+1rem\)\]{
        top:calc(var(--header-height) + var(--navbar-height) + 1rem)
    }
    .right-0{
        right:calc(var(--spacing)*0)
    }
    .right-2{
        right:calc(var(--spacing)*2)
    }
    .right-3{
        right:calc(var(--spacing)*3)
    }
    .-bottom-2{
        bottom:calc(var(--spacing)*-2)
    }
    .bottom-0{
        bottom:calc(var(--spacing)*0)
    }
    .bottom-0\.5{
        bottom:calc(var(--spacing)*.5)
    }
    .bottom-2{
        bottom:calc(var(--spacing)*2)
    }
    .bottom-4{
        bottom:calc(var(--spacing)*4)
    }
    .bottom-5{
        bottom:calc(var(--spacing)*5)
    }
    .left-0{
        left:calc(var(--spacing)*0)
    }
    .left-1\/2,.left-2\/4{
        left:50%
    }
    .left-6{
        left:calc(var(--spacing)*6)
    }
    .left-\[2\.2px\]{
        left:2.2px
    }
    .left-\[2px\]{
        left:2px
    }
    .left-auto{
        left:auto
    }
    .z-0{
        z-index:0
    }
    .z-1{
        z-index:1
    }
    .z-4{
        z-index:4
    }
    .z-5{
        z-index:5
    }
    .z-10{
        z-index:10
    }
    .z-20{
        z-index:20
    }
    .order-1{
        order:1
    }
    .order-2{
        order:2
    }
    .col-span-1{
        grid-column:span 1/span 1
    }
    .col-span-2{
        grid-column:span 2/span 2
    }
    .container{
        width:100%
    }
    @media (min-width:40rem){
        .container{
            max-width:40rem
        }
    }
    @media (min-width:48rem){
        .container{
            max-width:48rem
        }
    }
    @media (min-width:64rem){
        .container{
            max-width:64rem
        }
    }
    @media (min-width:80rem){
        .container{
            max-width:80rem
        }
    }
    @media (min-width:96rem){
        .container{
            max-width:96rem
        }
    }
    .m-5{
        margin:calc(var(--spacing)*5)
    }
    .m-\[15px\]{
        margin:15px
    }
    .mx-0\.5{
        margin-inline:calc(var(--spacing)*.5)
    }
    .mx-1{
        margin-inline:calc(var(--spacing)*1)
    }
    .mx-1\.5{
        margin-inline:calc(var(--spacing)*1.5)
    }
    .mx-2{
        margin-inline:calc(var(--spacing)*2)
    }
    .mx-3\.5{
        margin-inline:calc(var(--spacing)*3.5)
    }
    .mx-4{
        margin-inline:calc(var(--spacing)*4)
    }
    .mx-5{
        margin-inline:calc(var(--spacing)*5)
    }
    .mx-7\.5{
        margin-inline:calc(var(--spacing)*7.5)
    }
    .mx-auto{
        margin-inline:auto
    }
    .my-0\.5{
        margin-block:calc(var(--spacing)*.5)
    }
    .my-1{
        margin-block:calc(var(--spacing)*1)
    }
    .my-1\.5{
        margin-block:calc(var(--spacing)*1.5)
    }
    .my-2{
        margin-block:calc(var(--spacing)*2)
    }
    .my-2\.5{
        margin-block:calc(var(--spacing)*2.5)
    }
    .my-3{
        margin-block:calc(var(--spacing)*3)
    }
    .my-5{
        margin-block:calc(var(--spacing)*5)
    }
    .my-7\.5{
        margin-block:calc(var(--spacing)*7.5)
    }
    .my-\[3\%\]{
        margin-block:3%
    }
    .-ms-1{
        margin-inline-start:calc(var(--spacing)*-1)
    }
    .-ms-2{
        margin-inline-start:calc(var(--spacing)*-2)
    }
    .-ms-2\.5{
        margin-inline-start:calc(var(--spacing)*-2.5)
    }
    .ms-1{
        margin-inline-start:calc(var(--spacing)*1)
    }
    .ms-2{
        margin-inline-start:calc(var(--spacing)*2)
    }
    .ms-2\.5{
        margin-inline-start:calc(var(--spacing)*2.5)
    }
    .ms-5{
        margin-inline-start:calc(var(--spacing)*5)
    }
    .ms-7\.5{
        margin-inline-start:calc(var(--spacing)*7.5)
    }
    .ms-auto{
        margin-inline-start:auto
    }
    .-me-1{
        margin-inline-end:calc(var(--spacing)*-1)
    }
    .-me-1\.5{
        margin-inline-end:calc(var(--spacing)*-1.5)
    }
    .-me-2{
        margin-inline-end:calc(var(--spacing)*-2)
    }
    .me-0\.5{
        margin-inline-end:calc(var(--spacing)*.5)
    }
    .me-1{
        margin-inline-end:calc(var(--spacing)*1)
    }
    .me-1\.5{
        margin-inline-end:calc(var(--spacing)*1.5)
    }
    .me-1\.25{
        margin-inline-end:calc(var(--spacing)*1.25)
    }
    .me-2{
        margin-inline-end:calc(var(--spacing)*2)
    }
    .me-2\.5{
        margin-inline-end:calc(var(--spacing)*2.5)
    }
    .me-3{
        margin-inline-end:calc(var(--spacing)*3)
    }
    .me-5{
        margin-inline-end:calc(var(--spacing)*5)
    }
    .me-\[-10px\]{
        margin-inline-end:-10px
    }
    .\!mt-\[30px\]{
        margin-top:30px!important
    }
    .-mt-0\.5{
        margin-top:calc(var(--spacing)*-.5)
    }
    .-mt-1{
        margin-top:calc(var(--spacing)*-1)
    }
    .-mt-3{
        margin-top:calc(var(--spacing)*-3)
    }
    .-mt-7\.5{
        margin-top:calc(var(--spacing)*-7.5)
    }
    .-mt-8{
        margin-top:calc(var(--spacing)*-8)
    }
    .-mt-px{
        margin-top:-1px
    }
    .mt-0{
        margin-top:calc(var(--spacing)*0)
    }
    .mt-1{
        margin-top:calc(var(--spacing)*1)
    }
    .mt-2{
        margin-top:calc(var(--spacing)*2)
    }
    .mt-2\.5{
        margin-top:calc(var(--spacing)*2.5)
    }
    .mt-3{
        margin-top:calc(var(--spacing)*3)
    }
    .mt-4{
        margin-top:calc(var(--spacing)*4)
    }
    .mt-5{
        margin-top:calc(var(--spacing)*5)
    }
    .mt-6{
        margin-top:calc(var(--spacing)*6)
    }
    .mt-7{
        margin-top:calc(var(--spacing)*7)
    }
    .mt-\[2px\]{
        margin-top:2px
    }
    .mt-\[7px\]{
        margin-top:7px
    }
    .mr-1{
        margin-right:calc(var(--spacing)*1)
    }
    .mr-2{
        margin-right:calc(var(--spacing)*2)
    }
    .mr-5{
        margin-right:calc(var(--spacing)*5)
    }
    .mb-0\.5{
        margin-bottom:calc(var(--spacing)*.5)
    }
    .mb-1{
        margin-bottom:calc(var(--spacing)*1)
    }
    .mb-1\.5{
        margin-bottom:calc(var(--spacing)*1.5)
    }
    .mb-2{
        margin-bottom:calc(var(--spacing)*2)
    }
    .mb-2\.5{
        margin-bottom:calc(var(--spacing)*2.5)
    }
    .mb-3{
        margin-bottom:calc(var(--spacing)*3)
    }
    .mb-3\.5{
        margin-bottom:calc(var(--spacing)*3.5)
    }
    .mb-4{
        margin-bottom:calc(var(--spacing)*4)
    }
    .mb-4\.5{
        margin-bottom:calc(var(--spacing)*4.5)
    }
    .mb-5{
        margin-bottom:calc(var(--spacing)*5)
    }
    .mb-6\.5{
        margin-bottom:calc(var(--spacing)*6.5)
    }
    .mb-7{
        margin-bottom:calc(var(--spacing)*7)
    }
    .mb-7\.5{
        margin-bottom:calc(var(--spacing)*7.5)
    }
    .mb-8{
        margin-bottom:calc(var(--spacing)*8)
    }
    .mb-9{
        margin-bottom:calc(var(--spacing)*9)
    }
    .mb-10{
        margin-bottom:calc(var(--spacing)*10)
    }
    .mb-12{
        margin-bottom:calc(var(--spacing)*12)
    }
    .mb-16{
        margin-bottom:calc(var(--spacing)*16)
    }
    .mb-px{
        margin-bottom:1px
    }
    .-ml-1{
        margin-left:calc(var(--spacing)*-1)
    }
    .ml-1{
        margin-left:calc(var(--spacing)*1)
    }
    .ml-2{
        margin-left:calc(var(--spacing)*2)
    }
    .ml-5{
        margin-left:calc(var(--spacing)*5)
    }
    .block{
        display:block
    }
    .contents{
        display:contents
    }
    .flex{
        display:flex
    }
    .grid{
        display:grid
    }
    .hidden{
        display:none
    }
    .inline{
        display:inline
    }
    .inline-block{
        display:inline-block
    }
    .inline-flex{
        display:inline-flex
    }
    .table{
        display:table
    }
    .aspect-video{
        aspect-ratio:var(--aspect-video)
    }
    .size-0\.75{
        width:calc(var(--spacing)*.75);
        height:calc(var(--spacing)*.75)
    }
    .size-1{
        width:calc(var(--spacing)*1);
        height:calc(var(--spacing)*1)
    }
    .size-1\.5{
        width:calc(var(--spacing)*1.5);
        height:calc(var(--spacing)*1.5)
    }
    .size-2{
        width:calc(var(--spacing)*2);
        height:calc(var(--spacing)*2)
    }
    .size-2\.5{
        width:calc(var(--spacing)*2.5);
        height:calc(var(--spacing)*2.5)
    }
    .size-3{
        width:calc(var(--spacing)*3);
        height:calc(var(--spacing)*3)
    }
    .size-3\.5{
        width:calc(var(--spacing)*3.5);
        height:calc(var(--spacing)*3.5)
    }
    .size-4{
        width:calc(var(--spacing)*4);
        height:calc(var(--spacing)*4)
    }
    .size-5{
        width:calc(var(--spacing)*5);
        height:calc(var(--spacing)*5)
    }
    .size-6{
        width:calc(var(--spacing)*6);
        height:calc(var(--spacing)*6)
    }
    .size-7{
        width:calc(var(--spacing)*7);
        height:calc(var(--spacing)*7)
    }
    .size-7\.5{
        width:calc(var(--spacing)*7.5);
        height:calc(var(--spacing)*7.5)
    }
    .size-8{
        width:calc(var(--spacing)*8);
        height:calc(var(--spacing)*8)
    }
    .size-9{
        width:calc(var(--spacing)*9);
        height:calc(var(--spacing)*9)
    }
    .size-10{
        width:calc(var(--spacing)*10);
        height:calc(var(--spacing)*10)
    }
    .size-11{
        width:calc(var(--spacing)*11);
        height:calc(var(--spacing)*11)
    }
    .size-12{
        width:calc(var(--spacing)*12);
        height:calc(var(--spacing)*12)
    }
    .size-14{
        width:calc(var(--spacing)*14);
        height:calc(var(--spacing)*14)
    }
    .size-15{
        width:calc(var(--spacing)*15);
        height:calc(var(--spacing)*15)
    }
    .size-16{
        width:calc(var(--spacing)*16);
        height:calc(var(--spacing)*16)
    }
    .size-20{
        width:calc(var(--spacing)*20);
        height:calc(var(--spacing)*20)
    }
    .size-48{
        width:calc(var(--spacing)*48);
        height:calc(var(--spacing)*48)
    }
    .size-80{
        width:calc(var(--spacing)*80);
        height:calc(var(--spacing)*80)
    }
    .size-\[5px\]{
        width:5px;
        height:5px
    }
    .size-\[11px\]{
        width:11px;
        height:11px
    }
    .size-\[18px\]{
        width:18px;
        height:18px
    }
    .size-\[30px\]{
        width:30px;
        height:30px
    }
    .size-\[34px\]{
        width:34px;
        height:34px
    }
    .size-\[44px\]{
        width:44px;
        height:44px
    }
    .size-\[45px\]{
        width:45px;
        height:45px
    }
    .size-\[50px\]{
        width:50px;
        height:50px
    }
    .size-\[60px\]{
        width:60px;
        height:60px
    }
    .size-\[70px\]{
        width:70px;
        height:70px
    }
    .size-\[90px\]{
        width:90px;
        height:90px
    }
    .size-\[100px\]{
        width:100px;
        height:100px
    }
    .size-\[120px\]{
        width:120px;
        height:120px
    }
    .size-\[140px\]{
        width:140px;
        height:140px
    }
    .h-\(--header-height\){
        height:var(--header-height)
    }
    .h-\(--navbar-height\){
        height:var(--navbar-height)
    }
    .h-1{
        height:calc(var(--spacing)*1)
    }
    .h-1\.5{
        height:calc(var(--spacing)*1.5)
    }
    .h-2{
        height:calc(var(--spacing)*2)
    }
    .h-3\.5{
        height:calc(var(--spacing)*3.5)
    }
    .h-4{
        height:calc(var(--spacing)*4)
    }
    .h-5{
        height:calc(var(--spacing)*5)
    }
    .h-6{
        height:calc(var(--spacing)*6)
    }
    .h-7{
        height:calc(var(--spacing)*7)
    }
    .h-8{
        height:calc(var(--spacing)*8)
    }
    .h-8\.5{
        height:calc(var(--spacing)*8.5)
    }
    .h-9{
        height:calc(var(--spacing)*9)
    }
    .h-10{
        height:calc(var(--spacing)*10)
    }
    .h-11{
        height:calc(var(--spacing)*11)
    }
    .h-12{
        height:calc(var(--spacing)*12)
    }
    .h-20{
        height:calc(var(--spacing)*20)
    }
    .h-24{
        height:calc(var(--spacing)*24)
    }
    .h-40{
        height:calc(var(--spacing)*40)
    }
    .h-44{
        height:calc(var(--spacing)*44)
    }
    .h-48{
        height:calc(var(--spacing)*48)
    }
    .h-56{
        height:calc(var(--spacing)*56)
    }
    .h-\[1\.875rem\]{
        height:1.875rem
    }
    .h-\[4px\]{
        height:4px
    }
    .h-\[8px\]{
        height:8px
    }
    .h-\[12px\]{
        height:12px
    }
    .h-\[22px\]{
        height:22px
    }
    .h-\[28px\]{
        height:28px
    }
    .h-\[30px\]{
        height:30px
    }
    .h-\[35px\]{
        height:35px
    }
    .h-\[36px\]{
        height:36px
    }
    .h-\[42px\]{
        height:42px
    }
    .h-\[45px\]{
        height:45px
    }
    .h-\[50px\]{
        height:50px
    }
    .h-\[60px\]{
        height:60px
    }
    .h-\[70px\]{
        height:70px
    }
    .h-\[95\%\]{
        height:95%
    }
    .h-\[100px\]{
        height:100px
    }
    .h-\[120px\]{
        height:120px
    }
    .h-\[140px\]{
        height:140px
    }
    .h-\[170px\]{
        height:170px
    }
    .h-\[180px\]{
        height:180px
    }
    .h-\[240px\]{
        height:240px
    }
    .h-\[250px\]{
        height:250px
    }
    .h-\[280px\]{
        height:280px
    }
    .h-auto{
        height:auto
    }
    .h-full{
        height:100%
    }
    .h-px{
        height:1px
    }
    .max-h-5{
        max-height:calc(var(--spacing)*5)
    }
    .max-h-20{
        max-height:calc(var(--spacing)*20)
    }
    .max-h-36{
        max-height:calc(var(--spacing)*36)
    }
    .max-h-44{
        max-height:calc(var(--spacing)*44)
    }
    .max-h-\[25px\]{
        max-height:25px
    }
    .max-h-\[50vh\]{
        max-height:50vh
    }
    .max-h-\[55px\]{
        max-height:55px
    }
    .max-h-\[100px\]{
        max-height:100px
    }
    .max-h-\[113px\]{
        max-height:113px
    }
    .max-h-\[120px\]{
        max-height:120px
    }
    .max-h-\[130px\]{
        max-height:130px
    }
    .max-h-\[140px\]{
        max-height:140px
    }
    .max-h-\[150px\]{
        max-height:150px
    }
    .max-h-\[160px\]{
        max-height:160px
    }
    .max-h-\[170px\]{
        max-height:170px
    }
    .max-h-\[180px\]{
        max-height:180px
    }
    .max-h-\[200px\]{
        max-height:200px
    }
    .max-h-\[230px\]{
        max-height:230px
    }
    .max-h-\[250px\]{
        max-height:250px
    }
    .max-h-\[300px\]{
        max-height:300px
    }
    .max-h-\[400px\]{
        max-height:400px
    }
    .max-h-\[calc\(100dvh-10px\)\)\]{
        max-height:calc(100dvh - 10px)
    }
    .min-h-8\.5{
        min-height:calc(var(--spacing)*8.5)
    }
    .min-h-52{
        min-height:calc(var(--spacing)*52)
    }
    .min-h-80{
        min-height:calc(var(--spacing)*80)
    }
    .min-h-\[22px\]{
        min-height:22px
    }
    .min-h-\[24px\]{
        min-height:24px
    }
    .min-h-\[30px\]{
        min-height:30px
    }
    .min-h-\[34px\]{
        min-height:34px
    }
    .min-h-\[42px\]{
        min-height:42px
    }
    .min-h-\[44px\]{
        min-height:44px
    }
    .min-h-\[340px\]{
        min-height:340px
    }
    .min-h-\[400px\]{
        min-height:400px
    }
    .min-h-\[600px\]{
        min-height:600px
    }
    .w-\(--sidebar-width\){
        width:var(--sidebar-width)
    }
    .w-1\.5{
        width:calc(var(--spacing)*1.5)
    }
    .w-5{
        width:calc(var(--spacing)*5)
    }
    .w-6{
        width:calc(var(--spacing)*6)
    }
    .w-7{
        width:calc(var(--spacing)*7)
    }
    .w-8{
        width:calc(var(--spacing)*8)
    }
    .w-9{
        width:calc(var(--spacing)*9)
    }
    .w-10{
        width:calc(var(--spacing)*10)
    }
    .w-11{
        width:calc(var(--spacing)*11)
    }
    .w-12{
        width:calc(var(--spacing)*12)
    }
    .w-14{
        width:calc(var(--spacing)*14)
    }
    .w-16{
        width:calc(var(--spacing)*16)
    }
    .w-20{
        width:calc(var(--spacing)*20)
    }
    .w-24{
        width:calc(var(--spacing)*24)
    }
    .w-28{
        width:calc(var(--spacing)*28)
    }
    .w-36{
        width:calc(var(--spacing)*36)
    }
    .w-40{
        width:calc(var(--spacing)*40)
    }
    .w-48{
        width:calc(var(--spacing)*48)
    }
    .w-52{
        width:calc(var(--spacing)*52)
    }
    .w-56{
        width:calc(var(--spacing)*56)
    }
    .w-80{
        width:calc(var(--spacing)*80)
    }
    .w-\[1\.875rem\]{
        width:1.875rem
    }
    .w-\[2px\]{
        width:2px
    }
    .w-\[6px\]{
        width:6px
    }
    .w-\[20px\]{
        width:20px
    }
    .w-\[26px\]{
        width:26px
    }
    .w-\[30px\]{
        width:30px
    }
    .w-\[36px\]{
        width:36px
    }
    .w-\[40px\]{
        width:40px
    }
    .w-\[50px\]{
        width:50px
    }
    .w-\[55px\]{
        width:55px
    }
    .w-\[60px\]{
        width:60px
    }
    .w-\[62px\]{
        width:62px
    }
    .w-\[70px\]{
        width:70px
    }
    .w-\[75px\]{
        width:75px
    }
    .w-\[80px\]{
        width:80px
    }
    .w-\[90\%\]{
        width:90%
    }
    .w-\[90px\]{
        width:90px
    }
    .w-\[100px\]{
        width:100px
    }
    .w-\[105px\]{
        width:105px
    }
    .w-\[110px\]{
        width:110px
    }
    .w-\[120px\]{
        width:120px
    }
    .w-\[125px\]{
        width:125px
    }
    .w-\[135px\]{
        width:135px
    }
    .w-\[150px\]{
        width:150px
    }
    .w-\[170px\]{
        width:170px
    }
    .w-\[175px\]{
        width:175px
    }
    .w-\[180px\]{
        width:180px
    }
    .w-\[182px\]{
        width:182px
    }
    .w-\[185px\]{
        width:185px
    }
    .w-\[200px\]{
        width:200px
    }
    .w-\[220px\]{
        width:220px
    }
    .w-\[225px\]{
        width:225px
    }
    .w-\[230px\]{
        width:230px
    }
    .w-\[240px\]{
        width:240px
    }
    .w-\[250px\]{
        width:250px
    }
    .w-\[280px\]{
        width:280px
    }
    .w-\[285px\]{
        width:285px
    }
    .w-\[320px\]{
        width:320px
    }
    .w-\[350px\]{
        width:350px
    }
    .w-\[450px\]{
        width:450px
    }
    .w-\[520px\]{
        width:520px
    }
    .w-\[600px\]{
        width:600px
    }
    .w-\[720px\]{
        width:720px
    }
    .w-\[940px\]{
        width:940px
    }
    .w-auto{
        width:auto
    }
    .w-full{
        width:100%
    }
    .w-screen{
        width:100vw
    }
    .max-w-2xl{
        max-width:var(--container-2xl)
    }
    .max-w-16{
        max-width:calc(var(--spacing)*16)
    }
    .max-w-24{
        max-width:calc(var(--spacing)*24)
    }
    .max-w-32{
        max-width:calc(var(--spacing)*32)
    }
    .max-w-48{
        max-width:calc(var(--spacing)*48)
    }
    .max-w-56{
        max-width:calc(var(--spacing)*56)
    }
    .max-w-64{
        max-width:calc(var(--spacing)*64)
    }
    .max-w-96{
        max-width:calc(var(--spacing)*96)
    }
    .max-w-\[10\%\]{
        max-width:10%
    }
    .max-w-\[15\%\]{
        max-width:15%
    }
    .max-w-\[20\%\]{
        max-width:20%
    }
    .max-w-\[25\%\]{
        max-width:25%
    }
    .max-w-\[30\%\]{
        max-width:30%
    }
    @media (min-width: 1024px) {
        .lg\:max-w-\[30\%\] {
            max-width: 30%;
        }
    }    
    .max-w-\[60\%\]{
        max-width:60%
    }
    .max-w-\[70\%\]{
        max-width:70%
    }
    .max-w-\[90\%\]{
        max-width:90%
    }
    .max-w-\[125px\]{
        max-width:125px
    }
    .max-w-\[175px\]{
        max-width:175px
    }
    .max-w-\[200px\]{
        max-width:200px
    }
    .max-w-\[220px\]{
        max-width:220px
    }
    .max-w-\[250px\]{
        max-width:250px
    }
    .max-w-\[280px\]{
        max-width:280px
    }
    .max-w-\[320px\]{
        max-width:320px
    }
    .max-w-\[370px\]{
        max-width:370px
    }
    .max-w-\[380px\]{
        max-width:380px
    }
    .max-w-\[420px\]{
        max-width:420px
    }
    .max-w-\[440px\]{
        max-width:440px
    }
    .max-w-\[500px\]{
        max-width:500px
    }
    .max-w-\[600px\]{
        max-width:600px
    }
    .max-w-\[800px\]{
        max-width:800px
    }
    .max-w-\[1000px\]{
        max-width:1000px
    }
    .max-w-\[1200px\]{
        max-width:1200px
    }
    .max-w-full{
        max-width:100%
    }
    .max-w-none{
        max-width:none
    }
    .min-w-0{
        min-width:calc(var(--spacing)*0)
    }
    .min-w-12{
        min-width:calc(var(--spacing)*12)
    }
    .min-w-14{
        min-width:calc(var(--spacing)*14)
    }
    .min-w-16{
        min-width:calc(var(--spacing)*16)
    }
    .min-w-20{
        min-width:calc(var(--spacing)*20)
    }
    .min-w-24{
        min-width:calc(var(--spacing)*24)
    }
    .min-w-28{
        min-width:calc(var(--spacing)*28)
    }
    .min-w-32{
        min-width:calc(var(--spacing)*32)
    }
    .min-w-36{
        min-width:calc(var(--spacing)*36)
    }
    .min-w-40{
        min-width:calc(var(--spacing)*40)
    }
    .min-w-48{
        min-width:calc(var(--spacing)*48)
    }
    .min-w-52{
        min-width:calc(var(--spacing)*52)
    }
    .min-w-56{
        min-width:calc(var(--spacing)*56)
    }
    .min-w-60{
        min-width:calc(var(--spacing)*60)
    }
    .min-w-64{
        min-width:calc(var(--spacing)*64)
    }
    .min-w-72{
        min-width:calc(var(--spacing)*72)
    }
    .min-w-\[65px\]{
        min-width:65px
    }
    .min-w-\[66px\]{
        min-width:66px
    }
    .min-w-\[70px\]{
        min-width:70px
    }
    .min-w-\[73px\]{
        min-width:73px
    }
    .min-w-\[75px\]{
        min-width:75px
    }
    .min-w-\[77px\]{
        min-width:77px
    }
    .min-w-\[80px\]{
        min-width:80px
    }
    .min-w-\[82px\]{
        min-width:82px
    }
    .min-w-\[84px\]{
        min-width:84px
    }
    .min-w-\[85px\]{
        min-width:85px
    }
    .min-w-\[88px\]{
        min-width:88px
    }
    .min-w-\[90px\]{
        min-width:90px
    }
    .min-w-\[95px\]{
        min-width:95px
    }
    .min-w-\[98px\]{
        min-width:98px
    }
    .min-w-\[100px\]{
        min-width:100px
    }
    .min-w-\[103px\]{
        min-width:103px
    }
    .min-w-\[105px\]{
        min-width:105px
    }
    .min-w-\[106px\]{
        min-width:106px
    }
    .min-w-\[110px\]{
        min-width:110px
    }
    .min-w-\[115px\]{
        min-width:115px
    }
    .min-w-\[116px\]{
        min-width:116px
    }
    .min-w-\[120px\]{
        min-width:120px
    }
    .min-w-\[122px\]{
        min-width:122px
    }
    .min-w-\[125px\]{
        min-width:125px
    }
    .min-w-\[130px\]{
        min-width:130px
    }
    .min-w-\[137px\]{
        min-width:137px
    }
    .min-w-\[140px\]{
        min-width:140px
    }
    .min-w-\[150px\]{
        min-width:150px
    }
    .min-w-\[160px\]{
        min-width:160px
    }
    .min-w-\[165px\]{
        min-width:165px
    }
    .min-w-\[170px\]{
        min-width:170px
    }
    .min-w-\[175px\]{
        min-width:175px
    }
    .min-w-\[180px\]{
        min-width:180px
    }
    .min-w-\[185px\]{
        min-width:185px
    }
    .min-w-\[190px\]{
        min-width:190px
    }
    .min-w-\[198px\]{
        min-width:198px
    }
    .min-w-\[200px\]{
        min-width:200px
    }
    .min-w-\[201px\]{
        min-width:201px
    }
    .min-w-\[206px\]{
        min-width:206px
    }
    .min-w-\[220px\]{
        min-width:220px
    }
    .min-w-\[224px\]{
        min-width:224px
    }
    .min-w-\[225px\]{
        min-width:225px
    }
    .min-w-\[240px\]{
        min-width:240px
    }
    .min-w-\[250px\]{
        min-width:250px
    }
    .min-w-\[255px\]{
        min-width:255px
    }
    .min-w-\[260px\]{
        min-width:260px
    }
    .min-w-\[300px\]{
        min-width:300px
    }
    .min-w-\[1000px\]{
        min-width:1000px
    }
    .min-w-full{
        min-width:100%
    }
    .flex-1{
        flex:1
    }
    .flex-shrink{
        flex-shrink:1
    }
    .shrink-0{
        flex-shrink:0
    }
    .flex-grow,.grow{
        flex-grow:1
    }
    .grow-0{
        flex-grow:0
    }
    .basis-1\/2{
        flex-basis:50%
    }
    .basis-1\/4{
        flex-basis:25%
    }
    .table-auto{
        table-layout:auto
    }
    .table-fixed{
        table-layout:fixed
    }
    .border-collapse{
        border-collapse:collapse
    }
    .border-separate{
        border-collapse:separate
    }
    .border-spacing-0{
        --tw-border-spacing-x:calc(var(--spacing)*0);
        --tw-border-spacing-y:calc(var(--spacing)*0);
        border-spacing:var(--tw-border-spacing-x)var(--tw-border-spacing-y)
    }
    .-translate-1\/2{
        --tw-translate-x:calc(calc(1/2*100%)*-1);
        --tw-translate-y:calc(calc(1/2*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .-translate-x-1\/2{
        --tw-translate-x:calc(calc(1/2*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .-translate-x-2\/4{
        --tw-translate-x:calc(calc(2/4*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .translate-x-1\/2{
        --tw-translate-x:calc(1/2*100%);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .translate-x-full{
        --tw-translate-x:100%;
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .-translate-y-1\/2{
        --tw-translate-y:calc(calc(1/2*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .-translate-y-2\/4{
        --tw-translate-y:calc(calc(2/4*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .translate-y-0{
        --tw-translate-y:calc(var(--spacing)*0);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .translate-y-1\/2{
        --tw-translate-y:calc(1/2*100%);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .translate-y-2{
        --tw-translate-y:calc(var(--spacing)*2);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .transform{
        transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)
    }
    .animate-spin{
        animation:var(--animate-spin)
    }
    .cursor-not-allowed{
        cursor:not-allowed
    }
    .cursor-pointer{
        cursor:pointer
    }
    .resize{
        resize:both
    }
    .appearance-none{
        appearance:none
    }
    .grid-cols-1{
        grid-template-columns:repeat(1,minmax(0,1fr))
    }
    .grid-cols-2{
        grid-template-columns:repeat(2,minmax(0,1fr))
    }
    .grid-cols-3{
        grid-template-columns:repeat(3,minmax(0,1fr))
    }
    .grid-cols-4{
        grid-template-columns:repeat(4,minmax(0,1fr))
    }
    .grid-rows-2{
        grid-template-rows:repeat(2,minmax(0,1fr))
    }
    .flex-col{
        flex-direction:column
    }
    .flex-col-reverse{
        flex-direction:column-reverse
    }
    .flex-row{
        flex-direction:row
    }
    .flex-nowrap{
        flex-wrap:nowrap
    }
    .flex-wrap{
        flex-wrap:wrap
    }
    .place-content-between{
        place-content:space-between
    }
    .place-content-center{
        place-content:center
    }
    .place-items-center{
        place-items:center
    }
    .content-between{
        align-content:space-between
    }
    .items-baseline{
        align-items:baseline
    }
    .items-center{
        align-items:center
    }
    .items-end{
        align-items:flex-end
    }
    .items-start{
        align-items:flex-start
    }
    .items-stretch{
        align-items:stretch
    }
    .justify-between{
        justify-content:space-between
    }
    .justify-center{
        justify-content:center
    }
    .justify-end{
        justify-content:flex-end
    }
    .justify-start{
        justify-content:flex-start
    }
    .gap-0{
        gap:calc(var(--spacing)*0)
    }
    .gap-0\.5{
        gap:calc(var(--spacing)*.5)
    }
    .gap-1{
        gap:calc(var(--spacing)*1)
    }
    .gap-1\.5{
        gap:calc(var(--spacing)*1.5)
    }
    .gap-1\.25{
        gap:calc(var(--spacing)*1.25)
    }
    .gap-2{
        gap:calc(var(--spacing)*2)
    }
    .gap-2\.5{
        gap:calc(var(--spacing)*2.5)
    }
    .gap-3{
        gap:calc(var(--spacing)*3)
    }
    .gap-3\.5{
        gap:calc(var(--spacing)*3.5)
    }
    .gap-4{
        gap:calc(var(--spacing)*4)
    }
    .gap-4\.5{
        gap:calc(var(--spacing)*4.5)
    }
    .gap-5{
        gap:calc(var(--spacing)*5)
    }
    .gap-6{
        gap:calc(var(--spacing)*6)
    }
    .gap-7{
        gap:calc(var(--spacing)*7)
    }
    .gap-7\.5{
        gap:calc(var(--spacing)*7.5)
    }
    .gap-8{
        gap:calc(var(--spacing)*8)
    }
    .gap-9{
        gap:calc(var(--spacing)*9)
    }
    .gap-10{
        gap:calc(var(--spacing)*10)
    }
    .gap-\[5px\]{
        gap:5px
    }
    .gap-\[10px\]{
        gap:10px
    }
    .gap-\[14px\]{
        gap:14px
    }
    .gap-px{
        gap:1px
    }
    :where(.space-y-2>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))
    }
    :where(.space-y-2\.5>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*2.5)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*2.5)*calc(1 - var(--tw-space-y-reverse)))
    }
    :where(.space-y-3>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))
    }
    :where(.space-y-3\.5>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*3.5)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*3.5)*calc(1 - var(--tw-space-y-reverse)))
    }
    :where(.space-y-4>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))
    }
    :where(.space-y-4\.5>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*4.5)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*4.5)*calc(1 - var(--tw-space-y-reverse)))
    }
    :where(.space-y-5>:not(:last-child)){
        --tw-space-y-reverse:0;
        margin-block-start:calc(calc(var(--spacing)*5)*var(--tw-space-y-reverse));
        margin-block-end:calc(calc(var(--spacing)*5)*calc(1 - var(--tw-space-y-reverse)))
    }
    .gap-x-4{
        column-gap:calc(var(--spacing)*4)
    }
    :where(.-space-x-2>:not(:last-child)){
        --tw-space-x-reverse:0;
        margin-inline-start:calc(calc(var(--spacing)*-2)*var(--tw-space-x-reverse));
        margin-inline-end:calc(calc(var(--spacing)*-2)*calc(1 - var(--tw-space-x-reverse)))
    }
    :where(.space-x-0\.5>:not(:last-child)){
        --tw-space-x-reverse:0;
        margin-inline-start:calc(calc(var(--spacing)*.5)*var(--tw-space-x-reverse));
        margin-inline-end:calc(calc(var(--spacing)*.5)*calc(1 - var(--tw-space-x-reverse)))
    }
    :where(.space-x-2>:not(:last-child)){
        --tw-space-x-reverse:0;
        margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));
        margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))
    }
    .gap-y-3\.5{
        row-gap:calc(var(--spacing)*3.5)
    }
    .gap-y-5{
        row-gap:calc(var(--spacing)*5)
    }
    :where(.divide-y>:not(:last-child)){
        --tw-divide-y-reverse:0;
        border-bottom-style:var(--tw-border-style);
        border-top-style:var(--tw-border-style);
        border-top-width:calc(1px*var(--tw-divide-y-reverse));
        border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))
    }
    :where(.divide-border>:not(:last-child)){
        border-color:var(--border)
    }
    .place-self-end{
        place-self:end
    }
    .justify-self-center{
        justify-self:center
    }
    .justify-self-end{
        justify-self:flex-end
    }
    .truncate{
        text-overflow:ellipsis;
        white-space:nowrap;
        overflow:hidden
    }
    .overflow-auto{
        overflow:auto
    }
    .overflow-hidden{
        overflow:hidden
    }
    .rounded{
        border-radius:.25rem
    }
    .rounded-\[9px\]{
        border-radius:9px
    }
    .rounded-\[30px\]{
        border-radius:30px
    }
    .rounded-full{
        border-radius:3.40282e38px
    }
    .rounded-lg{
        border-radius:var(--radius)
    }
    .rounded-md{
        border-radius:calc(var(--radius) - 2px)
    }
    .rounded-l-md {
        border-top-left-radius: calc(var(--radius) - 2px);
        border-bottom-left-radius: calc(var(--radius) - 2px);
    }
    
    .rounded-r-md {
        border-top-right-radius: calc(var(--radius) - 2px);
        border-bottom-right-radius: calc(var(--radius) - 2px);
    }
    
    .rounded-sm{
        border-radius:calc(var(--radius) - 4px)
    }
    .rounded-xl{
        border-radius:calc(var(--radius) + 4px)
    }
    .rounded-xs{
        border-radius:var(--radius-xs)
    }
    .rounded-t{
        border-top-left-radius:.25rem;
        border-top-right-radius:.25rem
    }
    .rounded-t-lg{
        border-top-left-radius:var(--radius);
        border-top-right-radius:var(--radius)
    }
    .rounded-t-xl{
        border-top-left-radius:calc(var(--radius) + 4px);
        border-top-right-radius:calc(var(--radius) + 4px)
    }
    .rounded-b{
        border-bottom-right-radius:.25rem;
        border-bottom-left-radius:.25rem
    }
    .rounded-b-xl{
        border-bottom-right-radius:calc(var(--radius) + 4px);
        border-bottom-left-radius:calc(var(--radius) + 4px)
    }
    .border{
        border-style:var(--tw-border-style);
        border-width:1px
    }
    .border-0{
        border-style:var(--tw-border-style);
        border-width:0
    }
    .border-2{
        border-style:var(--tw-border-style);
        border-width:2px
    }
    .border-3{
        border-style:var(--tw-border-style);
        border-width:3px
    }
    .border-\[0\.5px\]{
        border-style:var(--tw-border-style);
        border-width:.5px
    }
    .border-x{
        border-inline-style:var(--tw-border-style);
        border-inline-width:1px
    }
    .border-y{
        border-block-style:var(--tw-border-style);
        border-block-width:1px
    }
    .border-s{
        border-inline-start-style:var(--tw-border-style);
        border-inline-start-width:1px
    }
    .border-s-2{
        border-inline-start-style:var(--tw-border-style);
        border-inline-start-width:2px
    }
    .border-e{
        border-inline-end-style:var(--tw-border-style);
        border-inline-end-width:1px
    }
    .border-t{
        border-top-style:var(--tw-border-style);
        border-top-width:1px
    }
    .border-r{
        border-right-style:var(--tw-border-style);
        border-right-width:1px
    }
    .border-b{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px
    }
    .border-b-0{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:0
    }
    .border-b-2{
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:2px
    }
    .border-l{
        border-left-style:var(--tw-border-style);
        border-left-width:1px
    }
    .border-dashed{
        --tw-border-style:dashed;
        border-style:dashed
    }
    .border-none{
        --tw-border-style:none;
        border-style:none
    }
    .border-black\/10\!{
        border-color:#0000001a!important
    }
    @supports (color:color-mix(in lab, red, red)){
        .border-black\/10\!{
            border-color:color-mix(in oklab,var(--color-black)10%,transparent)!important
        }
    }
    .border-blue-100{
        border-color:var(--color-blue-100)
    }
    .border-blue-500{
        border-color:var(--color-blue-500)
    }
    .border-border{
        border-color:var(--border)
    }
    .border-destructive\/10{
        border-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .border-destructive\/10{
            border-color:color-mix(in oklab,var(--destructive)10%,transparent)
        }
    }
    .border-gray-200{
        border-color:var(--color-gray-200)
    }
    .border-gray-300{
        border-color:var(--color-gray-300)
    }
    .border-green-200{
        border-color:var(--color-green-200)
    }
    .border-green-500{
        border-color:var(--color-green-500)
    }
    .border-input{
        border-color:var(--input)
    }
    .border-mono\/25{
        border-color:var(--mono)
    }
    @supports (color:color-mix(in lab, red, red)){
        .border-mono\/25{
            border-color:color-mix(in oklab,var(--mono)25%,transparent)
        }
    }
    .border-muted-foreground{
        border-color:var(--muted-foreground)
    }
    .border-orange-200{
        border-color:var(--color-orange-200)
    }
    .border-primary,.border-primary\/10{
        border-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .border-primary\/10{
            border-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    .border-transparent{
        border-color:#0000
    }
    .border-violet-200{
        border-color:var(--color-violet-200)
    }
    .border-white{
        border-color:var(--color-white)
    }
    .border-yellow-500{
        border-color:var(--color-yellow-500)
    }
    .border-zinc-300{
        border-color:var(--color-zinc-300)
    }
    .border-s-border{
        border-inline-start-color:var(--border)
    }
    .border-s-input{
        border-inline-start-color:var(--input)
    }
    .border-e-border{
        border-inline-end-color:var(--border)
    }
    .border-e-input{
        border-inline-end-color:var(--input)
    }
    .border-t-border{
        border-top-color:var(--border)
    }
    .border-r-input{
        border-right-color:var(--input)
    }
    .border-b-border{
        border-bottom-color:var(--border)
    }
    .border-b-input{
        border-bottom-color:var(--input)
    }
    .border-b-primary\/10{
        border-bottom-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .border-b-primary\/10{
            border-bottom-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    .border-b-transparent{
        border-bottom-color:#0000
    }
    .bg-accent\/50{
        background-color:var(--accent)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-accent\/50{
            background-color:color-mix(in oklab,var(--accent)50%,transparent)
        }
    }
    .bg-accent\/60{
        background-color:var(--accent)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-accent\/60{
            background-color:color-mix(in oklab,var(--accent)60%,transparent)
        }
    }
    .bg-background,.bg-background\/70{
        background-color:var(--background)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-background\/70{
            background-color:color-mix(in oklab,var(--background)70%,transparent)
        }
    }
    .bg-black\/25{
        background-color:#00000040
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-black\/25{
            background-color:color-mix(in oklab,var(--color-black)25%,transparent)
        }
    }
    .bg-blue-50{
        background-color:var(--color-blue-50)
    }
    .bg-blue-100{
        background-color:var(--color-blue-100)
    }
    .bg-blue-500{
        background-color:var(--color-blue-500)
    }
    .bg-blue-600{
        background-color:var(--color-blue-600)
    }
    .bg-border{
        background-color:var(--border)
    }
    .bg-destructive,.bg-destructive\/5{
        background-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-destructive\/5{
            background-color:color-mix(in oklab,var(--destructive)5%,transparent)
        }
    }
    .bg-gray-50{
        background-color:var(--color-gray-50)
    }
    .bg-gray-100{
        background-color:var(--color-gray-100)
    }
    .bg-gray-700{
        background-color:var(--color-gray-700)
    }
    .bg-green-50{
        background-color:var(--color-green-50)
    }
    .bg-green-500{
        background-color:var(--color-green-500)
    }
    .bg-input{
        background-color:var(--input)
    }
    .bg-mono,.bg-mono\/30{
        background-color:var(--mono)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-mono\/30{
            background-color:color-mix(in oklab,var(--mono)30%,transparent)
        }
    }
    .bg-mono\/50{
        background-color:var(--mono)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-mono\/50{
            background-color:color-mix(in oklab,var(--mono)50%,transparent)
        }
    }
    .bg-muted{
        background-color:var(--muted)
    }
    .bg-muted\!{
        background-color:var(--muted)!important
    }
    .bg-muted-foreground\/60{
        background-color:var(--muted-foreground)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-muted-foreground\/60{
            background-color:color-mix(in oklab,var(--muted-foreground)60%,transparent)
        }
    }
    .bg-muted\/25{
        background-color:var(--muted)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-muted\/25{
            background-color:color-mix(in oklab,var(--muted)25%,transparent)
        }
    }
    .bg-muted\/30{
        background-color:var(--muted)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-muted\/30{
            background-color:color-mix(in oklab,var(--muted)30%,transparent)
        }
    }
    .bg-muted\/40{
        background-color:var(--muted)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-muted\/40{
            background-color:color-mix(in oklab,var(--muted)40%,transparent)
        }
    }
    .bg-muted\/50{
        background-color:var(--muted)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-muted\/50{
            background-color:color-mix(in oklab,var(--muted)50%,transparent)
        }
    }
    .bg-muted\/70{
        background-color:var(--muted)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-muted\/70{
            background-color:color-mix(in oklab,var(--muted)70%,transparent)
        }
    }
    .bg-orange-100{
        background-color:var(--color-orange-100)
    }
    .bg-primary,.bg-primary\/5{
        background-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-primary\/5{
            background-color:color-mix(in oklab,var(--primary)5%,transparent)
        }
    }
    .bg-primary\/10{
        background-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-primary\/10{
            background-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    .bg-secondary-foreground{
        background-color:var(--secondary-foreground)
    }
    .bg-transparent{
        background-color:#0000
    }
    .bg-transparent\!{
        background-color:#0000!important
    }
    .bg-violet-50{
        background-color:var(--color-violet-50)
    }
    .bg-violet-500{
        background-color:var(--color-violet-500)
    }
    .bg-white{
        background-color:var(--color-white)
    }
    .bg-white\/70{
        background-color:#ffffffb3
    }
    @supports (color:color-mix(in lab, red, red)){
        .bg-white\/70{
            background-color:color-mix(in oklab,var(--color-white)70%,transparent)
        }
    }
    .bg-yellow-50{
        background-color:var(--color-yellow-50)
    }
    .bg-yellow-500{
        background-color:var(--color-yellow-500)
    }
    .bg-linear-to-t{
        --tw-gradient-position:to top;
        background-image:linear-gradient(var(--tw-gradient-stops))
    }
    @supports (background-image:linear-gradient(in lab, red, red)){
        .bg-linear-to-t{
            --tw-gradient-position:to top in oklab
        }
    }
    .from-3\%{
        --tw-gradient-from-position:3%
    }
    .to-transparent{
        --tw-gradient-to:transparent;
        --tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))
    }
    .bg-\[length\:80\%\]{
        background-size:80%
    }
    .bg-\[length\:85\%\]{
        background-size:85%
    }
    .bg-\[length\:500px\]{
        background-size:500px
    }
    .bg-\[length\:550px\]{
        background-size:550px
    }
    .bg-\[length\:600px\]{
        background-size:600px
    }
    .bg-\[length\:650px\]{
        background-size:650px
    }
    .bg-\[length\:660px_310px\]{
        background-size:660px 310px
    }
    .bg-\[length\:700px\]{
        background-size:700px
    }
    .bg-\[length\:750px\]{
        background-size:750px
    }
    .bg-cover{
        background-size:cover
    }
    .\[background-position\:7\.5rem_-3\.5rem\]{
        background-position:7.5rem -3.5rem
    }
    .\[background-position\:9rem_-4rem\]{
        background-position:9rem -4rem
    }
    .\[background-position\:121\%_41\%\]{
        background-position:121% 41%
    }
    .\[background-position\:175\%_25\%\]{
        background-position:175% 25%
    }
    .\[background-position\:195px_-85px\]{
        background-position:195px -85px
    }
    .bg-\[center_right_-8rem\]{
        background-position:right -8rem center
    }
    .bg-\[center_top_1\.3rem\]{
        background-position:50% 1.3rem
    }
    .bg-\[right_top_-1\.7rem\]{
        background-position:right top -1.7rem
    }
    .bg-center{
        background-position:50%
    }
    .bg-top{
        background-position:top
    }
    .bg-no-repeat{
        background-repeat:no-repeat
    }
    .fill-border{
        fill:var(--border)
    }
    .fill-destructive\/5{
        fill:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .fill-destructive\/5{
            fill:color-mix(in oklab,var(--destructive)5%,transparent)
        }
    }
    .fill-green-50{
        fill:var(--color-green-50)
    }
    .fill-green-100{
        fill:var(--color-green-100)
    }
    .fill-muted\/30{
        fill:var(--muted)
    }
    @supports (color:color-mix(in lab, red, red)){
        .fill-muted\/30{
            fill:color-mix(in oklab,var(--muted)30%,transparent)
        }
    }
    .fill-primary\/5{
        fill:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .fill-primary\/5{
            fill:color-mix(in oklab,var(--primary)5%,transparent)
        }
    }
    .fill-violet-50{
        fill:var(--color-violet-50)
    }
    .fill-violet-100{
        fill:var(--color-violet-100)
    }
    .fill-yellow-100{
        fill:var(--color-yellow-100)
    }
    .stroke-border{
        stroke:var(--border)
    }
    .stroke-destructive\/10{
        stroke:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .stroke-destructive\/10{
            stroke:color-mix(in oklab,var(--destructive)10%,transparent)
        }
    }
    .stroke-green-200{
        stroke:var(--color-green-200)
    }
    .stroke-input{
        stroke:var(--input)
    }
    .stroke-primary\/10{
        stroke:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .stroke-primary\/10{
            stroke:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    .stroke-violet-100{
        stroke:var(--color-violet-100)
    }
    .stroke-violet-200{
        stroke:var(--color-violet-200)
    }
    .stroke-yellow-100{
        stroke:var(--color-yellow-100)
    }
    .stroke-yellow-200{
        stroke:var(--color-yellow-200)
    }
    .p-0{
        padding:calc(var(--spacing)*0)
    }
    .p-0\.5{
        padding:calc(var(--spacing)*.5)
    }
    .p-1{
        padding:calc(var(--spacing)*1)
    }
    .p-1\.5{
        padding:calc(var(--spacing)*1.5)
    }
    .p-2{
        padding:calc(var(--spacing)*2)
    }
    .p-2\.5{
        padding:calc(var(--spacing)*2.5)
    }
    .p-3{
        padding:calc(var(--spacing)*3)
    }
    .p-3\.5{
        padding:calc(var(--spacing)*3.5)
    }
    .p-4{
        padding:calc(var(--spacing)*4)
    }
    .p-5{
        padding:calc(var(--spacing)*5)
    }
    .p-5\!{
        padding:calc(var(--spacing)*5)!important
    }
    .p-7{
        padding:calc(var(--spacing)*7)
    }
    .p-7\.5{
        padding:calc(var(--spacing)*7.5)
    }
    .p-8{
        padding:calc(var(--spacing)*8)
    }
    .p-10{
        padding:calc(var(--spacing)*10)
    }
    .px-0{
        padding-inline:calc(var(--spacing)*0)
    }
    .px-0\.5{
        padding-inline:calc(var(--spacing)*.5)
    }
    .px-1{
        padding-inline:calc(var(--spacing)*1)
    }
    .px-1\.5{
        padding-inline:calc(var(--spacing)*1.5)
    }
    .px-2{
        padding-inline:calc(var(--spacing)*2)
    }
    .px-2\.5{
        padding-inline:calc(var(--spacing)*2.5)
    }
    .px-2\.75{
        padding-inline:calc(var(--spacing)*2.75)
    }
    .px-3{
        padding-inline:calc(var(--spacing)*3)
    }
    .px-3\.5{
        padding-inline:calc(var(--spacing)*3.5)
    }
    .px-4{
        padding-inline:calc(var(--spacing)*4)
    }
    .px-5{
        padding-inline:calc(var(--spacing)*5)
    }
    .px-5\!{
        padding-inline:calc(var(--spacing)*5)!important
    }
    .px-7{
        padding-inline:calc(var(--spacing)*7)
    }
    .px-7\.5{
        padding-inline:calc(var(--spacing)*7.5)
    }
    .px-9{
        padding-inline:calc(var(--spacing)*9)
    }
    .px-10{
        padding-inline:calc(var(--spacing)*10)
    }
    .py-0{
        padding-block:calc(var(--spacing)*0)
    }
    .py-0\.5{
        padding-block:calc(var(--spacing)*.5)
    }
    .py-1{
        padding-block:calc(var(--spacing)*1)
    }
    .py-1\.5{
        padding-block:calc(var(--spacing)*1.5)
    }
    .py-2{
        padding-block:calc(var(--spacing)*2)
    }
    .py-2\.5{
        padding-block:calc(var(--spacing)*2.5)
    }
    .py-2\.25{
        padding-block:calc(var(--spacing)*2.25)
    }
    .py-3{
        padding-block:calc(var(--spacing)*3)
    }
    .py-3\.5{
        padding-block:calc(var(--spacing)*3.5)
    }
    .py-3\.5\!{
        padding-block:calc(var(--spacing)*3.5)!important
    }
    .py-4{
        padding-block:calc(var(--spacing)*4)
    }
    .py-4\.5{
        padding-block:calc(var(--spacing)*4.5)
    }
    .py-5{
        padding-block:calc(var(--spacing)*5)
    }
    .py-5\.5\!{
        padding-block:calc(var(--spacing)*5.5)!important
    }
    .py-7\.5{
        padding-block:calc(var(--spacing)*7.5)
    }
    .py-9{
        padding-block:calc(var(--spacing)*9)
    }
    .py-10{
        padding-block:calc(var(--spacing)*10)
    }
    .py-\[6px\]{
        padding-block:6px
    }
    .py-\[8px\]{
        padding-block:8px
    }
    .ps-1\.5{
        padding-inline-start:calc(var(--spacing)*1.5)
    }
    .ps-2{
        padding-inline-start:calc(var(--spacing)*2)
    }
    .ps-2\.5{
        padding-inline-start:calc(var(--spacing)*2.5)
    }
    .ps-3{
        padding-inline-start:calc(var(--spacing)*3)
    }
    .ps-4{
        padding-inline-start:calc(var(--spacing)*4)
    }
    .ps-5{
        padding-inline-start:calc(var(--spacing)*5)
    }
    .ps-6{
        padding-inline-start:calc(var(--spacing)*6)
    }
    .ps-7{
        padding-inline-start:calc(var(--spacing)*7)
    }
    .ps-8{
        padding-inline-start:calc(var(--spacing)*8)
    }
    .ps-12{
        padding-inline-start:calc(var(--spacing)*12)
    }
    .ps-\[10px\]{
        padding-inline-start:10px
    }
    .ps-\[14\.5px\]{
        padding-inline-start:14.5px
    }
    .ps-\[22px\]{
        padding-inline-start:22px
    }
    .ps-px{
        padding-inline-start:1px
    }
    .pe-1{
        padding-inline-end:calc(var(--spacing)*1)
    }
    .pe-2{
        padding-inline-end:calc(var(--spacing)*2)
    }
    .pe-2\.5{
        padding-inline-end:calc(var(--spacing)*2.5)
    }
    .pe-3{
        padding-inline-end:calc(var(--spacing)*3)
    }
    .pe-3\.5{
        padding-inline-end:calc(var(--spacing)*3.5)
    }
    .pe-4{
        padding-inline-end:calc(var(--spacing)*4)
    }
    .pe-5{
        padding-inline-end:calc(var(--spacing)*5)
    }
    .pe-6{
        padding-inline-end:calc(var(--spacing)*6)
    }
    .pe-7\.5{
        padding-inline-end:calc(var(--spacing)*7.5)
    }
    .pe-10{
        padding-inline-end:calc(var(--spacing)*10)
    }
    .pe-\[10px\]{
        padding-inline-end:10px
    }
    .pt-\(--header-height\){
        padding-top:var(--header-height)
    }
    .pt-0{
        padding-top:calc(var(--spacing)*0)
    }
    .pt-0\.5{
        padding-top:calc(var(--spacing)*.5)
    }
    .pt-1{
        padding-top:calc(var(--spacing)*1)
    }
    .pt-1\.5{
        padding-top:calc(var(--spacing)*1.5)
    }
    .pt-2{
        padding-top:calc(var(--spacing)*2)
    }
    .pt-2\.5{
        padding-top:calc(var(--spacing)*2.5)
    }
    .pt-2\.25{
        padding-top:calc(var(--spacing)*2.25)
    }
    .pt-3{
        padding-top:calc(var(--spacing)*3)
    }
    .pt-3\.5{
        padding-top:calc(var(--spacing)*3.5)
    }
    .pt-4{
        padding-top:calc(var(--spacing)*4)
    }
    .pt-5{
        padding-top:calc(var(--spacing)*5)
    }
    .pt-6{
        padding-top:calc(var(--spacing)*6)
    }
    .pt-7{
        padding-top:calc(var(--spacing)*7)
    }
    .pt-7\.5{
        padding-top:calc(var(--spacing)*7.5)
    }
    .pt-7\.5\!{
        padding-top:calc(var(--spacing)*7.5)!important
    }
    .pt-8{
        padding-top:calc(var(--spacing)*8)
    }
    .pt-\[1px\]{
        padding-top:1px
    }
    .pr-2{
        padding-right:calc(var(--spacing)*2)
    }
    .pr-2\.5{
        padding-right:calc(var(--spacing)*2.5)
    }
    .pr-3{
        padding-right:calc(var(--spacing)*3)
    }
    .pb-0{
        padding-bottom:calc(var(--spacing)*0)
    }
    .pb-0\.5{
        padding-bottom:calc(var(--spacing)*.5)
    }
    .pb-1{
        padding-bottom:calc(var(--spacing)*1)
    }
    .pb-1\.5{
        padding-bottom:calc(var(--spacing)*1.5)
    }
    .pb-2{
        padding-bottom:calc(var(--spacing)*2)
    }
    .pb-2\.5{
        padding-bottom:calc(var(--spacing)*2.5)
    }
    .pb-3{
        padding-bottom:calc(var(--spacing)*3)
    }
    .pb-3\.5{
        padding-bottom:calc(var(--spacing)*3.5)
    }
    .pb-4{
        padding-bottom:calc(var(--spacing)*4)
    }
    .pb-5{
        padding-bottom:calc(var(--spacing)*5)
    }
    .pb-6{
        padding-bottom:calc(var(--spacing)*6)
    }
    .pb-7{
        padding-bottom:calc(var(--spacing)*7)
    }
    .pb-7\.5{
        padding-bottom:calc(var(--spacing)*7.5)
    }
    .pb-10{
        padding-bottom:calc(var(--spacing)*10)
    }
    .pb-px{
        padding-bottom:1px
    }
    .pl-2\.5{
        padding-left:calc(var(--spacing)*2.5)
    }
    .pl-6{
        padding-left:calc(var(--spacing)*6)
    }
    .text-center{
        text-align:center
    }
    .text-end{
        text-align:end
    }
    .text-left{
        text-align:left
    }
    .text-right{
        text-align:right
    }
    .text-start{
        text-align:start
    }
    .align-bottom{
        vertical-align:bottom
    }
    .align-middle{
        vertical-align:middle
    }
    .text-2sm{
        font-size:var(--text-2sm);
        line-height:var(--tw-leading,var(--text-2sm--line-height))
    }
    .text-2xl{
        font-size:var(--text-2xl);
        line-height:var(--tw-leading,var(--text-2xl--line-height))
    }
    .text-2xs{
        font-size:var(--text-2xs);
        line-height:var(--tw-leading,var(--text-2xs--line-height))
    }
    .text-3xl{
        font-size:var(--text-3xl);
        line-height:var(--tw-leading,var(--text-3xl--line-height))
    }
    .text-4xl{
        font-size:var(--text-4xl);
        line-height:var(--tw-leading,var(--text-4xl--line-height))
    }
    .text-base{
        font-size:var(--text-base);
        line-height:var(--tw-leading,var(--text-base--line-height))
    }
    .text-base\!{
        font-size:var(--text-base)!important;
        line-height:var(--tw-leading,var(--text-base--line-height))!important
    }
    .text-lg{
        font-size:var(--text-lg);
        line-height:var(--tw-leading,var(--text-lg--line-height))
    }
    .text-sm{
        font-size:var(--text-sm);
        line-height:var(--tw-leading,var(--text-sm--line-height))
    }
    .text-xl{
        font-size:var(--text-xl);
        line-height:var(--tw-leading,var(--text-xl--line-height))
    }
    .text-xs{
        font-size:var(--text-xs);
        line-height:var(--tw-leading,var(--text-xs--line-height))
    }
    .text-xs\!{
        font-size:var(--text-xs)!important;
        line-height:var(--tw-leading,var(--text-xs--line-height))!important
    }
    .text-\[1\.875rem\]{
        font-size:1.875rem
    }
    .text-\[2\.25rem\]{
        font-size:2.25rem
    }
    .text-\[10px\]{
        font-size:10px
    }
    .text-\[11px\]{
        font-size:11px
    }
    .text-\[26px\]{
        font-size:26px
    }
    .leading-3{
        --tw-leading:calc(var(--spacing)*3);
        line-height:calc(var(--spacing)*3)
    }
    .leading-4{
        --tw-leading:calc(var(--spacing)*4);
        line-height:calc(var(--spacing)*4)
    }
    .leading-5{
        --tw-leading:calc(var(--spacing)*5);
        line-height:calc(var(--spacing)*5)
    }
    .leading-5\.5{
        --tw-leading:calc(var(--spacing)*5.5);
        line-height:calc(var(--spacing)*5.5)
    }
    .leading-6{
        --tw-leading:calc(var(--spacing)*6);
        line-height:calc(var(--spacing)*6)
    }
    .leading-\[14px\]{
        --tw-leading:14px;
        line-height:14px
    }
    .leading-\[22px\]{
        --tw-leading:22px;
        line-height:22px
    }
    .leading-none{
        --tw-leading:1;
        line-height:1
    }
    .font-medium{
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium)
    }
    .font-normal{
        --tw-font-weight:var(--font-weight-normal);
        font-weight:var(--font-weight-normal)
    }
    .font-semibold{
        --tw-font-weight:var(--font-weight-semibold);
        font-weight:var(--font-weight-semibold)
    }
    .font-bold{
        --tw-font-weight:var(--font-weight-bold);
        font-weight:var(--font-weight-bold)
    }
    .tracking-tight{
        --tw-tracking:var(--tracking-tight);
        letter-spacing:var(--tracking-tight)
    }
    .text-nowrap{
        text-wrap:nowrap
    }
    .whitespace-nowrap{
        white-space:nowrap
    }
    .text-accent-foreground\/60{
        color:var(--accent-foreground)
    }
    @supports (color:color-mix(in lab, red, red)){
        .text-accent-foreground\/60{
            color:color-mix(in oklab,var(--accent-foreground)60%,transparent)
        }
    }
    .text-blue-400{
        color:var(--color-blue-400)
    }
    .text-blue-600{
        color:var(--color-blue-600)
    }
    .text-blue-800{
        color:var(--color-blue-800)
    }
    .text-destructive{
        color:var(--destructive)
    }
    .text-foreground{
        color:var(--foreground)
    }
    .text-gray-300{
        color:var(--color-gray-300)
    }
    .text-gray-400{
        color:var(--color-gray-400)
    }
    .text-gray-500{
        color:var(--color-gray-500)
    }
    .text-gray-600{
        color:var(--color-gray-600)
    }
    .text-gray-700{
        color:var(--color-gray-700)
    }
    .text-gray-800{
        color:var(--color-gray-800)
    }
    .text-gray-900{
        color:var(--color-gray-900)
    }
    .text-green-500{
        color:var(--color-green-500)
    }
    .text-green-600{
        color:var(--color-green-600)
    }
    .text-mono{
        color:var(--mono)
    }
    .text-muted-foreground,.text-muted-foreground\/60{
        color:var(--muted-foreground)
    }
    @supports (color:color-mix(in lab, red, red)){
        .text-muted-foreground\/60{
            color:color-mix(in oklab,var(--muted-foreground)60%,transparent)
        }
    }
    .text-orange-400{
        color:var(--color-orange-400)
    }
    .text-primary{
        color:var(--primary)
    }
    .text-primary-foreground{
        color:var(--primary-foreground)
    }
    .text-red-500{
        color:var(--color-red-500)
    }
    .text-secondary-foreground{
        color:var(--secondary-foreground)
    }
    .text-violet-500{
        color:var(--color-violet-500)
    }
    .text-violet-600{
        color:var(--color-violet-600)
    }
    .text-white{
        color:var(--color-white)
    }
    .text-yellow-500{
        color:var(--color-yellow-500)
    }
    .text-yellow-600{
        color:var(--color-yellow-600)
    }
    .lowercase{
        text-transform:lowercase
    }
    .uppercase{
        text-transform:uppercase
    }
    .italic{
        font-style:italic
    }
    .line-through{
        text-decoration-line:line-through
    }
    .underline{
        text-decoration-line:underline
    }
    .antialiased{
        -webkit-font-smoothing:antialiased;
        -moz-osx-font-smoothing:grayscale
    }
    .opacity-0{
        opacity:0
    }
    .opacity-25{
        opacity:.25
    }
    .opacity-75{
        opacity:.75
    }
    .opacity-80{
        opacity:.8
    }
    .opacity-100{
        opacity:1
    }
    .shadow-\[0px_3px_11px_0px_rgba\(45\,72\,126\,0\.10\)\]{
        --tw-shadow:0px 3px 11px 0px var(--tw-shadow-color,#2d487e1a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
    }
    .shadow-lg{
        --tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
    }
    .shadow-md{
        --tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
    }
    .shadow-none{
        --tw-shadow:0 0 #0000;
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
    }
    .shadow-xs{
        --tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
    }
    .ring,.ring-1{
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
    }
    .ring-2{
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
    }
    .shadow-black\/5{
        --tw-shadow-color:#0000000d
    }
    @supports (color:color-mix(in lab, red, red)){
        .shadow-black\/5{
            --tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-black)5%,transparent)var(--tw-shadow-alpha),transparent)
        }
    }
    .ring-background{
        --tw-ring-color:var(--background)
    }
    .ring-blue-300{
        --tw-ring-color:var(--color-blue-300)
    }
    .ring-border{
        --tw-ring-color:var(--border)
    }
    .ring-destructive\/20{
        --tw-ring-color:var(--destructive)
    }
    @supports (color:color-mix(in lab, red, red)){
        .ring-destructive\/20{
            --tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)
        }
    }
    .ring-green-200{
        --tw-ring-color:var(--color-green-200)
    }
    .ring-input{
        --tw-ring-color:var(--input)
    }
    .ring-primary\/10{
        --tw-ring-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .ring-primary\/10{
            --tw-ring-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    .ring-primary\/20{
        --tw-ring-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .ring-primary\/20{
            --tw-ring-color:color-mix(in oklab,var(--primary)20%,transparent)
        }
    }
    .ring-violet-200{
        --tw-ring-color:var(--color-violet-200)
    }
    .ring-yellow-200{
        --tw-ring-color:var(--color-yellow-200)
    }
    .outline{
        outline-style:var(--tw-outline-style);
        outline-width:1px
    }
    .outline-2{
        outline-style:var(--tw-outline-style);
        outline-width:2px
    }
    .outline-3{
        outline-style:var(--tw-outline-style);
        outline-width:3px
    }
    .outline-background{
        outline-color:var(--background)
    }
    .outline-gray-50{
        outline-color:var(--color-gray-50)
    }
    .outline-muted\/70{
        outline-color:var(--muted)
    }
    @supports (color:color-mix(in lab, red, red)){
        .outline-muted\/70{
            outline-color:color-mix(in oklab,var(--muted)70%,transparent)
        }
    }
    .blur{
        --tw-blur:blur(8px);
        filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)
    }
    .filter{
        filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)
    }
    .backdrop-blur-md{
        --tw-backdrop-blur:blur(var(--blur-md));
        -webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);
        backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)
    }
    .transition{
        transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration))
    }
    .transition-\[height\]{
        transition-property:height;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration))
    }
    .transition-all{
        transition-property:all;
        transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));
        transition-duration:var(--tw-duration,var(--default-transition-duration))
    }
    .duration-300{
        --tw-duration:.3s;
        transition-duration:.3s
    }
    .\[--header-height-default\:95px\]{
        --header-height-default:95px
    }
    .\[--header-height-default\:100px\]{
        --header-height-default:100px
    }
    .\[--header-height-mobile\:70px\]{
        --header-height-mobile:70px
    }
    .\[--header-height\:54px\]{
        --header-height:54px
    }
    .\[--header-height\:58px\]{
        --header-height:58px
    }
    .\[--header-height\:60px\]{
        --header-height:60px
    }
    .\[--header-height\:78px\]{
        --header-height:78px
    }
    .\[--header-height\:var\(--header-height-default\)\]{
        --header-height:var(--header-height-default)
    }
    .\[--kt-drawer-enable\:true\]{
        --kt-drawer-enable:true
    }
    .\[--kt-reparent-mode\:prepend\]{
        --kt-reparent-mode:prepend
    }
    .\[--kt-reparent-target\:\#contentContainer\]{
        --kt-reparent-target:#contentContainer
    }
    .\[--kt-reparent-target\:body\]{
        --kt-reparent-target:body
    }
    .\[--kt-scrollbar-width\:auto\]{
        --kt-scrollbar-width:auto
    }
    .\[--navbar-height\:56px\]{
        --navbar-height:56px
    }
    .\[--sidebar-width\:58px\]{
        --sidebar-width:58px
    }
    .\[--sidebar-width\:90px\]{
        --sidebar-width:90px
    }
    .\[--sidebar-width\:200px\]{
        --sidebar-width:200px
    }
    .\[--sidebar-width\:270px\]{
        --sidebar-width:270px
    }
    .\[--sidebar-width\:290px\]{
        --sidebar-width:290px
    }
    :is(.\*\:border-border>*){
        border-color:var(--border)
    }
    .not-last\:block:not(:last-child){
        display:block
    }
    .not-last\:border-e:not(:last-child){
        border-inline-end-style:var(--tw-border-style);
        border-inline-end-width:1px
    }
    .not-last\:border-b:not(:last-child){
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px
    }
    @media (hover:hover){
        .group-hover\:text-primary:is(:where(.group):hover *){
            color:var(--primary)
        }
    }
    .group-has-checked\:hidden:is(:where(.group):has(:checked) *){
        display:none
    }
    .group-has-checked\:inline:is(:where(.group):has(:checked) *){
        display:inline
    }
    .placeholder\:text-secondary-foreground::placeholder{
        color:var(--secondary-foreground)
    }
    .before\:absolute:before{
        content:var(--tw-content);
        position:absolute
    }
    .before\:start-\[20px\]:before{
        content:var(--tw-content);
        inset-inline-start:20px
    }
    .before\:start-\[32px\]:before{
        content:var(--tw-content);
        inset-inline-start:32px
    }
    .before\:top-0:before{
        content:var(--tw-content);
        top:calc(var(--spacing)*0)
    }
    .before\:bottom-0:before{
        content:var(--tw-content);
        bottom:calc(var(--spacing)*0)
    }
    .before\:left-\[11px\]:before{
        content:var(--tw-content);
        left:11px
    }
    .before\:size-1\.5:before{
        content:var(--tw-content);
        width:calc(var(--spacing)*1.5);
        height:calc(var(--spacing)*1.5)
    }
    .before\:size-\[6px\]:before{
        content:var(--tw-content);
        width:6px;
        height:6px
    }
    .before\:-translate-x-2\/4:before{
        content:var(--tw-content);
        --tw-translate-x:calc(calc(2/4*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .before\:-translate-y-1\/2:before{
        content:var(--tw-content);
        --tw-translate-y:calc(calc(1/2*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .before\:-translate-y-2\/4:before{
        content:var(--tw-content);
        --tw-translate-y:calc(calc(2/4*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .before\:rounded-full:before{
        content:var(--tw-content);
        border-radius:3.40282e38px
    }
    .before\:border-s:before{
        content:var(--tw-content);
        border-inline-start-style:var(--tw-border-style);
        border-inline-start-width:1px
    }
    .before\:border-l:before{
        content:var(--tw-content);
        border-left-style:var(--tw-border-style);
        border-left-width:1px
    }
    .before\:border-border:before{
        content:var(--tw-content);
        border-color:var(--border)
    }
    .last\:me-5:last-child{
        margin-inline-end:calc(var(--spacing)*5)
    }
    .last\:mr-5:last-child{
        margin-right:calc(var(--spacing)*5)
    }
    .last-of-type\:hidden:last-of-type{
        display:none
    }
    @media (hover:hover){
        .hover\:z-5:hover{
            z-index:5
        }
        .hover\:rounded-lg:hover{
            border-radius:var(--radius)
        }
        .hover\:border-border:hover{
            border-color:var(--border)
        }
        .hover\:border-input:hover{
            border-color:var(--input)
        }
        .hover\:bg-accent\/60:hover{
            background-color:var(--accent)
        }
        @supports (color:color-mix(in lab, red, red)){
            .hover\:bg-accent\/60:hover{
                background-color:color-mix(in oklab,var(--accent)60%,transparent)
            }
        }
        .hover\:bg-background:hover{
            background-color:var(--background)
        }
        .hover\:bg-blue-50:hover{
            background-color:var(--color-blue-50)
        }
        .hover\:bg-blue-200:hover{
            background-color:var(--color-blue-200)
        }
        .hover\:bg-blue-600:hover{
            background-color:var(--color-blue-600)
        }
        .hover\:bg-blue-700:hover{
            background-color:var(--color-blue-700)
        }
        .hover\:bg-gray-100:hover{
            background-color:var(--color-gray-100)
        }
        .hover\:bg-gray-200:hover{
            background-color:var(--color-gray-200)
        }
        .hover\:bg-primary\/10:hover{
            background-color:var(--primary)
        }
        @supports (color:color-mix(in lab, red, red)){
            .hover\:bg-primary\/10:hover{
                background-color:color-mix(in oklab,var(--primary)10%,transparent)
            }
        }
        .hover\:bg-transparent:hover{
            background-color:#0000
        }
        .hover\:font-medium:hover{
            --tw-font-weight:var(--font-weight-medium);
            font-weight:var(--font-weight-medium)
        }
        .hover\:text-blue-600:hover{
            color:var(--color-blue-600)
        }
        .hover\:text-foreground:hover{
            color:var(--foreground)
        }
        .hover\:text-gray-600:hover{
            color:var(--color-gray-600)
        }
        .hover\:text-primary:hover{
            color:var(--primary)
        }
        .hover\:text-white:hover{
            color:var(--color-white)
        }
    }
    .focus\:border-blue-400:focus{
        border-color:var(--color-blue-400)
    }
    .focus\:border-primary\/10:focus{
        border-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .focus\:border-primary\/10:focus{
            border-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    .focus\:ring-2:focus{
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
    }
    .focus\:ring-3:focus{
        --tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);
        box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
    }
    .focus\:ring-blue-200:focus{
        --tw-ring-color:var(--color-blue-200)
    }
    .focus\:ring-blue-500:focus{
        --tw-ring-color:var(--color-blue-500)
    }
    .focus\:ring-primary\/10:focus{
        --tw-ring-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .focus\:ring-primary\/10:focus{
            --tw-ring-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    .focus\:outline-none:focus{
        --tw-outline-style:none;
        outline-style:none
    }
    :where(.authors-row) .in-\[\.authors-row\]\:start-\[64px\]{
        inset-inline-start:64px
    }
    :where(.authors-row) .in-\[\.authors-row\]\:size-\[80px\]{
        width:80px;
        height:80px
    }
    :where(.kt-menu-dropdown) .in-\[\.kt-menu-dropdown\]\:-rotate-90,:where(.menu-dropdown) .in-\[\.menu-dropdown\]\:-rotate-90{
        rotate:-90deg
    }
    .has-checked\:border-3:has(:checked){
        border-style:var(--tw-border-style);
        border-width:3px
    }
    .has-checked\:border-green-500:has(:checked){
        border-color:var(--color-green-500)
    }
    .has-checked\:border-primary:has(:checked){
        border-color:var(--primary)
    }
    @media (min-width:40rem){
        .sm\:ms-8{
            margin-inline-start:calc(var(--spacing)*8)
        }
        .sm\:me-0{
            margin-inline-end:calc(var(--spacing)*0)
        }
        .sm\:inline-block{
            display:inline-block
        }
        .sm\:max-w-full{
            max-width:100%
        }
        .sm\:grid-cols-1{
            grid-template-columns:repeat(1,minmax(0,1fr))
        }
        .sm\:grid-cols-2{
            grid-template-columns:repeat(2,minmax(0,1fr))
        }
        .sm\:grid-cols-4{
            grid-template-columns:repeat(4,minmax(0,1fr))
        }
        .sm\:flex-row{
            flex-direction:row
        }
        .sm\:flex-nowrap{
            flex-wrap:nowrap
        }
        .sm\:flex-wrap{
            flex-wrap:wrap
        }
        .sm\:items-center{
            align-items:center
        }
        .sm\:ps-8{
            padding-inline-start:calc(var(--spacing)*8)
        }
    }
    @media (min-width:48rem){
        .md\:order-1{
            order:1
        }
        .md\:order-2{
            order:2
        }
        .md\:block{
            display:block
        }
        .md\:hidden{
            display:none
        }
        .md\:inline{
            display:inline
        }
        .md\:w-80{
            width:calc(var(--spacing)*80)
        }
        .md\:max-w-\[60\%\]{
            max-width:60%
        }
        .md\:flex-1{
            flex:1
        }
        .md\:grid-cols-2{
            grid-template-columns:repeat(2,minmax(0,1fr))
        }
        .md\:flex-row{
            flex-direction:row
        }
        .md\:flex-nowrap{
            flex-wrap:nowrap
        }
        .md\:items-center{
            align-items:center
        }
        .md\:justify-between{
            justify-content:space-between
        }
        .md\:gap-10{
            gap:calc(var(--spacing)*10)
        }
    }
    @media (min-width:64rem){
        .lg\:fixed{
            position:fixed
        }
        .lg\:start-auto{
            inset-inline-start:auto
        }
        .lg\:top-\(--header-height\){
            top:var(--header-height)
        }
        .lg\:top-\[15\%\]{
            top:15%
        }
        .lg\:top-\[80px\]{
            top:80px
        }
        .lg\:right-auto{
            right:auto
        }
        .lg\:order-1{
            order:1
        }
        .lg\:order-2{
            order:2
        }
        .lg\:col-span-1{
            grid-column:span 1/span 1
        }
        .lg\:col-span-2{
            grid-column:span 2/span 2
        }
        .lg\:col-span-3{
            grid-column:span 3/span 3
        }
        .lg\:m-5{
            margin:calc(var(--spacing)*5)
        }
        .lg\:mx-0{
            margin-inline:calc(var(--spacing)*0)
        }
        .lg\:mx-3{
            margin-inline:calc(var(--spacing)*3)
        }
        .lg\:ms-\(--sidebar-width\){
            margin-inline-start:var(--sidebar-width)
        }
        .lg\:ms-4{
            margin-inline-start:calc(var(--spacing)*4)
        }
        .lg\:me-10{
            margin-inline-end:calc(var(--spacing)*10)
        }
        .lg\:mt-\(--navbar-height\){
            margin-top:var(--navbar-height)
        }
        .lg\:mt-0{
            margin-top:calc(var(--spacing)*0)
        }
        .lg\:mt-2{
            margin-top:calc(var(--spacing)*2)
        }
        .lg\:mt-5{
            margin-top:calc(var(--spacing)*5)
        }
        .lg\:mt-\[15px\]{
            margin-top:15px
        }
        .lg\:mb-0{
            margin-bottom:calc(var(--spacing)*0)
        }
        .lg\:mb-5{
            margin-bottom:calc(var(--spacing)*5)
        }
        .lg\:mb-6{
            margin-bottom:calc(var(--spacing)*6)
        }
        .lg\:mb-7{
            margin-bottom:calc(var(--spacing)*7)
        }
        .lg\:mb-7\.5{
            margin-bottom:calc(var(--spacing)*7.5)
        }
        .lg\:mb-8{
            margin-bottom:calc(var(--spacing)*8)
        }
        .lg\:mb-9{
            margin-bottom:calc(var(--spacing)*9)
        }
        .lg\:mb-10{
            margin-bottom:calc(var(--spacing)*10)
        }
        .lg\:mb-11{
            margin-bottom:calc(var(--spacing)*11)
        }
        .lg\:block{
            display:block
        }
        .lg\:flex{
            display:flex
        }
        .lg\:hidden{
            display:none
        }
        .lg\:inline{
            display:inline
        }
        .lg\:h-\(--header-height\){
            height:var(--header-height)
        }
        .lg\:h-\[50px\]{
            height:50px
        }
        .lg\:max-h-\[calc\(100dvh-70px\)\)\]{
            max-height:calc(100dvh - 70px)
        }
        .lg\:w-\(--sidebar-width\){
            width:var(--sidebar-width)
        }
        .lg\:w-60{
            width:calc(var(--spacing)*60)
        }
        .lg\:w-\[50px\]{
            width:50px
        }
        .lg\:w-\[240px\]{
            width:240px
        }
        .lg\:w-\[250px\]{
            width:250px
        }
        .lg\:w-\[260px\]{
            width:260px
        }
        .lg\:w-\[280px\]{
            width:280px
        }
        .lg\:w-\[400px\]{
            width:400px
        }
        .lg\:max-w-\[220px\]{
            max-width:220px
        }
        .lg\:max-w-\[600px\]{
            max-width:600px
        }
        .lg\:max-w-\[670px\]{
            max-width:670px
        }
        .lg\:max-w-\[700px\]{
            max-width:700px
        }
        .lg\:max-w-\[900px\]{
            max-width:900px
        }
        .lg\:max-w-\[1240px\]{
            max-width:1240px
        }
        .lg\:min-w-24{
            min-width:calc(var(--spacing)*24)
        }
        .lg\:grow-0{
            flex-grow:0
        }
        .lg\:grid-cols-1{
            grid-template-columns:repeat(1,minmax(0,1fr))
        }
        .lg\:grid-cols-2{
            grid-template-columns:repeat(2,minmax(0,1fr))
        }
        .lg\:grid-cols-3{
            grid-template-columns:repeat(3,minmax(0,1fr))
        }
        .lg\:grid-cols-4{
            grid-template-columns:repeat(4,minmax(0,1fr))
        }
        .lg\:grid-cols-5{
            grid-template-columns:repeat(5,minmax(0,1fr))
        }
        .lg\:flex-col{
            flex-direction:column
        }
        .lg\:flex-row{
            flex-direction:row
        }
        .lg\:flex-nowrap{
            flex-wrap:nowrap
        }
        .lg\:flex-wrap{
            flex-wrap:wrap
        }
        .lg\:items-center{
            align-items:center
        }
        .lg\:items-end{
            align-items:flex-end
        }
        .lg\:items-stretch{
            align-items:stretch
        }
        .lg\:justify-between{
            justify-content:space-between
        }
        .lg\:justify-center{
            justify-content:center
        }
        .lg\:justify-end{
            justify-content:flex-end
        }
        .lg\:gap-1\.5{
            gap:calc(var(--spacing)*1.5)
        }
        .lg\:gap-2{
            gap:calc(var(--spacing)*2)
        }
        .lg\:gap-2\.5{
            gap:calc(var(--spacing)*2.5)
        }
        .lg\:gap-3{
            gap:calc(var(--spacing)*3)
        }
        .lg\:gap-3\.5{
            gap:calc(var(--spacing)*3.5)
        }
        .lg\:gap-4{
            gap:calc(var(--spacing)*4)
        }
        .lg\:gap-4\.5{
            gap:calc(var(--spacing)*4.5)
        }
        .lg\:gap-5{
            gap:calc(var(--spacing)*5)
        }
        .lg\:gap-6{
            gap:calc(var(--spacing)*6)
        }
        .lg\:gap-7\.5{
            gap:calc(var(--spacing)*7.5)
        }
        .lg\:gap-9{
            gap:calc(var(--spacing)*9)
        }
        .lg\:gap-10{
            gap:calc(var(--spacing)*10)
        }
        .lg\:gap-11{
            gap:calc(var(--spacing)*11)
        }
        .lg\:gap-12{
            gap:calc(var(--spacing)*12)
        }
        .lg\:gap-14{
            gap:calc(var(--spacing)*14)
        }
        .lg\:gap-20{
            gap:calc(var(--spacing)*20)
        }
        .lg\:overflow-hidden{
            overflow:hidden
        }
        .lg\:overflow-visible{
            overflow:visible
        }
        .lg\:rounded-none{
            border-radius:0
        }
        .lg\:rounded-xl{
            border-radius:calc(var(--radius) + 4px)
        }
        .lg\:rounded-e-xl{
            border-start-end-radius:calc(var(--radius) + 4px);
            border-end-end-radius:calc(var(--radius) + 4px)
        }
        .lg\:rounded-t-none{
            border-top-left-radius:0;
            border-top-right-radius:0
        }
        .lg\:rounded-l-none{
            border-top-left-radius:0;
            border-bottom-left-radius:0
        }
        .lg\:rounded-l-xl{
            border-top-left-radius:calc(var(--radius) + 4px);
            border-bottom-left-radius:calc(var(--radius) + 4px)
        }
        .lg\:rounded-tl-xl{
            border-top-left-radius:calc(var(--radius) + 4px)
        }
        .lg\:rounded-r-none{
            border-top-right-radius:0;
            border-bottom-right-radius:0
        }
        .lg\:border{
            border-style:var(--tw-border-style);
            border-width:1px
        }
        .lg\:border-0{
            border-style:var(--tw-border-style);
            border-width:0
        }
        .lg\:border-s{
            border-inline-start-style:var(--tw-border-style);
            border-inline-start-width:1px
        }
        .lg\:border-e{
            border-inline-end-style:var(--tw-border-style);
            border-inline-end-width:1px
        }
        .lg\:border-t{
            border-top-style:var(--tw-border-style);
            border-top-width:1px
        }
        .lg\:border-r{
            border-right-style:var(--tw-border-style);
            border-right-width:1px
        }
        .lg\:border-border{
            border-color:var(--border)
        }
        .lg\:border-s-border{
            border-inline-start-color:var(--border)
        }
        .lg\:border-e-border{
            border-inline-end-color:var(--border)
        }
        .lg\:border-t-border{
            border-top-color:var(--border)
        }
        .lg\:border-r-border{
            border-right-color:var(--border)
        }
        .lg\:p-0{
            padding:calc(var(--spacing)*0)
        }
        .lg\:p-7{
            padding:calc(var(--spacing)*7)
        }
        .lg\:p-7\.5{
            padding:calc(var(--spacing)*7.5)
        }
        .lg\:p-10{
            padding:calc(var(--spacing)*10)
        }
        .lg\:p-12{
            padding:calc(var(--spacing)*12)
        }
        .lg\:p-16{
            padding:calc(var(--spacing)*16)
        }
        .lg\:px-6{
            padding-inline:calc(var(--spacing)*6)
        }
        .lg\:px-7{
            padding-inline:calc(var(--spacing)*7)
        }
        .lg\:px-7\.5{
            padding-inline:calc(var(--spacing)*7.5)
        }
        .lg\:px-10{
            padding-inline:calc(var(--spacing)*10)
        }
        .lg\:py-0{
            padding-block:calc(var(--spacing)*0)
        }
        .lg\:py-3\.5{
            padding-block:calc(var(--spacing)*3.5)
        }
        .lg\:py-4{
            padding-block:calc(var(--spacing)*4)
        }
        .lg\:py-5{
            padding-block:calc(var(--spacing)*5)
        }
        .lg\:py-6{
            padding-block:calc(var(--spacing)*6)
        }
        .lg\:py-7\.5{
            padding-block:calc(var(--spacing)*7.5)
        }
        .lg\:py-9{
            padding-block:calc(var(--spacing)*9)
        }
        .lg\:py-10{
            padding-block:calc(var(--spacing)*10)
        }
        .lg\:ps-0{
            padding-inline-start:calc(var(--spacing)*0)
        }
        .lg\:ps-4{
            padding-inline-start:calc(var(--spacing)*4)
        }
        .lg\:ps-5{
            padding-inline-start:calc(var(--spacing)*5)
        }
        .lg\:pe-0{
            padding-inline-end:calc(var(--spacing)*0)
        }
        .lg\:pe-3{
            padding-inline-end:calc(var(--spacing)*3)
        }
        .lg\:pe-5{
            padding-inline-end:calc(var(--spacing)*5)
        }
        .lg\:pe-6{
            padding-inline-end:calc(var(--spacing)*6)
        }
        .lg\:pe-8{
            padding-inline-end:calc(var(--spacing)*8)
        }
        .lg\:pe-10{
            padding-inline-end:calc(var(--spacing)*10)
        }
        .lg\:pe-12{
            padding-inline-end:calc(var(--spacing)*12)
        }
        .lg\:pe-12\.5{
            padding-inline-end:calc(var(--spacing)*12.5)
        }
        .lg\:pt-0{
            padding-top:calc(var(--spacing)*0)
        }
        .lg\:pt-4{
            padding-top:calc(var(--spacing)*4)
        }
        .lg\:pt-5{
            padding-top:calc(var(--spacing)*5)
        }
        .lg\:pt-6{
            padding-top:calc(var(--spacing)*6)
        }
        .lg\:pt-7{
            padding-top:calc(var(--spacing)*7)
        }
        .lg\:pt-7\.5{
            padding-top:calc(var(--spacing)*7.5)
        }
        .lg\:pt-9{
            padding-top:calc(var(--spacing)*9)
        }
        .lg\:pt-10{
            padding-top:calc(var(--spacing)*10)
        }
        .lg\:pr-12\.5{
            padding-right:calc(var(--spacing)*12.5)
        }
        .lg\:pb-0{
            padding-bottom:calc(var(--spacing)*0)
        }
        .lg\:pb-2\.5{
            padding-bottom:calc(var(--spacing)*2.5)
        }
        .lg\:pb-4{
            padding-bottom:calc(var(--spacing)*4)
        }
        .lg\:pb-5{
            padding-bottom:calc(var(--spacing)*5)
        }
        .lg\:pb-7{
            padding-bottom:calc(var(--spacing)*7)
        }
        .lg\:pb-7\.5{
            padding-bottom:calc(var(--spacing)*7.5)
        }
        .lg\:pb-9{
            padding-bottom:calc(var(--spacing)*9)
        }
        .lg\:pb-10{
            padding-bottom:calc(var(--spacing)*10)
        }
        .lg\:text-end{
            text-align:end
        }
        .lg\:text-right{
            text-align:right
        }
        .lg\:text-2xl{
            font-size:var(--text-2xl);
            line-height:var(--tw-leading,var(--text-2xl--line-height))
        }
        .lg\:text-sm{
            font-size:var(--text-sm);
            line-height:var(--tw-leading,var(--text-sm--line-height))
        }
        .lg\:\[--kt-drawer-enable\:false\]{
            --kt-drawer-enable:false
        }
        .lg\:\[--kt-reparent-mode\:prepend\]{
            --kt-reparent-mode:prepend
        }
        .lg\:\[--kt-reparent-target\:\#headerContainer\]{
            --kt-reparent-target:#headerContainer
        }
        .lg\:\[--kt-reparent-target\:\#megaMenuContainer\]{
            --kt-reparent-target:#megaMenuContainer
        }
        .lg\:\[--kt-reparent-target\:\#megaMenuWrapper\]{
            --kt-reparent-target:#megaMenuWrapper
        }
        .lg\:\[--kt-scrollbar-width\:auto\]{
            --kt-scrollbar-width:auto
        }
        .lg\:\[--scrollbar-width\:auto\]{
            --scrollbar-width:auto
        }
        .lg\:\[scrollbar-width\:auto\]{
            scrollbar-width:auto
        }
    }
    @media (min-width:80rem){
        .xl\:me-14{
            margin-inline-end:calc(var(--spacing)*14)
        }
        .xl\:me-16{
            margin-inline-end:calc(var(--spacing)*16)
        }
        .xl\:w-\[38\.75rem\]{
            width:38.75rem
        }
        .xl\:max-w-96{
            max-width:calc(var(--spacing)*96)
        }
        .xl\:min-w-24{
            min-width:calc(var(--spacing)*24)
        }
        .xl\:grid-cols-1{
            grid-template-columns:repeat(1,minmax(0,1fr))
        }
        .xl\:grid-cols-2{
            grid-template-columns:repeat(2,minmax(0,1fr))
        }
        .xl\:grid-cols-3{
            grid-template-columns:repeat(3,minmax(0,1fr))
        }
        .xl\:grid-cols-4{
            grid-template-columns:repeat(4,minmax(0,1fr))
        }
        .xl\:grid-cols-5{
            grid-template-columns:repeat(5,minmax(0,1fr))
        }
        .xl\:grid-cols-6{
            grid-template-columns:repeat(6,minmax(0,1fr))
        }
        .xl\:grid-cols-7{
            grid-template-columns:repeat(7,minmax(0,1fr))
        }
        .xl\:flex-row{
            flex-direction:row
        }
        .xl\:flex-nowrap{
            flex-wrap:nowrap
        }
        .xl\:gap-7\.5{
            gap:calc(var(--spacing)*7.5)
        }
        .xl\:bg-cover{
            background-size:cover
        }
    }
    @media (min-width:96rem){
        .\32 xl\:-ml-\[60px\]{
            margin-left:-60px
        }
    }
    .ltr\:rounded-tl-xl:where(:dir(ltr),[dir=ltr],[dir=ltr] *){
        border-top-left-radius:calc(var(--radius) + 4px)
    }
    .ltr\:rounded-tr-xl:where(:dir(ltr),[dir=ltr],[dir=ltr] *){
        border-top-right-radius:calc(var(--radius) + 4px)
    }
    .ltr\:border-l:where(:dir(ltr),[dir=ltr],[dir=ltr] *){
        border-left-style:var(--tw-border-style);
        border-left-width:1px
    }
    .rtl\:start-0:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        inset-inline-start:calc(var(--spacing)*0)
    }
    .rtl\:-translate-x-1\/2:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        --tw-translate-x:calc(calc(1/2*100%)*-1);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .rtl\:translate-x-1\/2:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        --tw-translate-x:calc(1/2*100%);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .rtl\:translate-x-2\/4:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        --tw-translate-x:calc(2/4*100%);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .rtl\:rotate-180:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        rotate:180deg
    }
    .rtl\:transform:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)
    }
    .rtl\:justify-start:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        justify-content:flex-start
    }
    .rtl\:rounded-tl-xl:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        border-top-left-radius:calc(var(--radius) + 4px)
    }
    .rtl\:rounded-tr-xl:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        border-top-right-radius:calc(var(--radius) + 4px)
    }
    .rtl\:border-s:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        border-inline-start-style:var(--tw-border-style);
        border-inline-start-width:1px
    }
    .rtl\:\[background-position\:-3rem_-3\.5rem\]:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        background-position:-3rem -3.5rem
    }
    .rtl\:\[background-position\:-4rem_-4rem\]:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        background-position:-4rem -4rem
    }
    .rtl\:\[background-position\:-30\%_41\%\]:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        background-position:-30% 41%
    }
    .rtl\:\[background-position\:-70\%_25\%\]:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        background-position:-70% 25%
    }
    .rtl\:\[background-position\:-195px_-85px\]:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        background-position:-195px -85px
    }
    .rtl\:bg-\[center_left_-8rem\]:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        background-position:-8rem
    }
    .rtl\:bg-\[left_top_-1\.7rem\]:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        background-position:0 -1.7rem
    }
    .rtl\:before\:translate-x-1\/2:where(:dir(rtl),[dir=rtl],[dir=rtl] *):before{
        content:var(--tw-content);
        --tw-translate-x:calc(1/2*100%);
        translate:var(--tw-translate-x)var(--tw-translate-y)
    }
    .dark\:block:is(.dark *){
        display:block
    }
    .dark\:hidden:is(.dark *){
        display:none
    }
    .dark\:inline-block:is(.dark *){
        display:inline-block
    }
    .dark\:border-b:is(.dark *){
        border-bottom-style:var(--tw-border-style);
        border-bottom-width:1px
    }
    .dark\:border-green-950:is(.dark *){
        border-color:var(--color-green-950)
    }
    .dark\:border-orange-950:is(.dark *){
        border-color:var(--color-orange-950)
    }
    .dark\:border-violet-950:is(.dark *){
        border-color:var(--color-violet-950)
    }
    .dark\:border-yellow-950:is(.dark *){
        border-color:var(--color-yellow-950)
    }
    .dark\:border-zinc-600:is(.dark *){
        border-color:var(--color-zinc-600)
    }
    .dark\:bg-background:is(.dark *),.dark\:bg-background\/70:is(.dark *){
        background-color:var(--background)
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark\:bg-background\/70:is(.dark *){
            background-color:color-mix(in oklab,var(--background)70%,transparent)
        }
    }
    .dark\:bg-green-950\/30:is(.dark *){
        background-color:#032e154d
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark\:bg-green-950\/30:is(.dark *){
            background-color:color-mix(in oklab,var(--color-green-950)30%,transparent)
        }
    }
    .dark\:bg-orange-950\/30:is(.dark *){
        background-color:#4413064d
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark\:bg-orange-950\/30:is(.dark *){
            background-color:color-mix(in oklab,var(--color-orange-950)30%,transparent)
        }
    }
    .dark\:bg-violet-950\/30:is(.dark *){
        background-color:#2f0d684d
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark\:bg-violet-950\/30:is(.dark *){
            background-color:color-mix(in oklab,var(--color-violet-950)30%,transparent)
        }
    }
    .dark\:bg-yellow-950\/30:is(.dark *){
        background-color:#4320044d
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark\:bg-yellow-950\/30:is(.dark *){
            background-color:color-mix(in oklab,var(--color-yellow-950)30%,transparent)
        }
    }
    .dark\:fill-green-950\/30:is(.dark *){
        fill:#032e154d
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark\:fill-green-950\/30:is(.dark *){
            fill:color-mix(in oklab,var(--color-green-950)30%,transparent)
        }
    }
    .dark\:fill-violet-950\/30:is(.dark *){
        fill:#2f0d684d
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark\:fill-violet-950\/30:is(.dark *){
            fill:color-mix(in oklab,var(--color-violet-950)30%,transparent)
        }
    }
    .dark\:fill-yellow-950\/30:is(.dark *){
        fill:#4320044d
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark\:fill-yellow-950\/30:is(.dark *){
            fill:color-mix(in oklab,var(--color-yellow-950)30%,transparent)
        }
    }
    .dark\:stroke-green-950:is(.dark *){
        stroke:var(--color-green-950)
    }
    .dark\:stroke-violet-950:is(.dark *){
        stroke:var(--color-violet-950)
    }
    .dark\:stroke-yellow-950:is(.dark *){
        stroke:var(--color-yellow-950)
    }
    .dark\:text-black\/30:is(.dark *){
        color:#0000004d
    }
    @supports (color:color-mix(in lab, red, red)){
        .dark\:text-black\/30:is(.dark *){
            color:color-mix(in oklab,var(--color-black)30%,transparent)
        }
    }
    .dark\:ring-green-950:is(.dark *){
        --tw-ring-color:var(--color-green-950)
    }
    .dark\:ring-violet-950:is(.dark *){
        --tw-ring-color:var(--color-violet-950)
    }
    .dark\:ring-yellow-950:is(.dark *){
        --tw-ring-color:var(--color-yellow-950)
    }
    .kt-accordion-active\:hidden[data-kt-accordion-item].active,[data-kt-accordion-item].active>[data-kt-accordion-toggle] .kt-accordion-active\:hidden,[data-kt-accordion-item].active>[data-kt-accordion-toggle].kt-accordion-active\:hidden{
        display:none
    }
    .kt-accordion-active\:inline-flex[data-kt-accordion-item].active,[data-kt-accordion-item].active>[data-kt-accordion-toggle] .kt-accordion-active\:inline-flex,[data-kt-accordion-item].active>[data-kt-accordion-toggle].kt-accordion-active\:inline-flex{
        display:inline-flex
    }
    [data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle].kt-dropdown-open\:bg-background,[data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle] .kt-dropdown-open\:bg-background{
        background-color:var(--background)
    }
    [data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle].kt-dropdown-open\:bg-primary\/10{
        background-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        [data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle].kt-dropdown-open\:bg-primary\/10{
            background-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    [data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle] .kt-dropdown-open\:bg-primary\/10{
        background-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        [data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle] .kt-dropdown-open\:bg-primary\/10{
            background-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    [data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle].kt-dropdown-open\:bg-transparent,[data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle] .kt-dropdown-open\:bg-transparent{
        background-color:#0000
    }
    [data-kt-image-input-initialized].empty .kt-image-input-empty\:border-input{
        border-color:var(--input)
    }
    .kt-modal-open\:\!flex[data-kt-modal-initialized].open,[data-kt-modal-initialized].open .kt-modal-open\:\!flex{
        display:flex!important
    }
    .kt-scrollspy-active\:font-medium[data-kt-scrollspy-anchor].active,[data-kt-scrollspy-anchor].active .kt-scrollspy-active\:font-medium{
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium)
    }
    .kt-scrollspy-active\:text-primary[data-kt-scrollspy-anchor].active,[data-kt-scrollspy-anchor].active .kt-scrollspy-active\:text-primary{
        color:var(--primary)
    }
    .kt-scrollspy-active\:before\:bg-primary[data-kt-scrollspy-anchor].active:before,[data-kt-scrollspy-anchor].active .kt-scrollspy-active\:before\:bg-primary:before{
        content:var(--tw-content);
        background-color:var(--primary)
    }
    .kt-tab-active\:border-primary\/10[data-kt-tab-toggle].active{
        border-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-tab-active\:border-primary\/10[data-kt-tab-toggle].active{
            border-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    [data-kt-tab-toggle].active .kt-tab-active\:border-primary\/10{
        border-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        [data-kt-tab-toggle].active .kt-tab-active\:border-primary\/10{
            border-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    [data-kt-tabs-initialized] [data-kt-dropdown-initialized]:has([data-kt-tab-toggle].active) .kt-tab-active\:border-primary\/10{
        border-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        [data-kt-tabs-initialized] [data-kt-dropdown-initialized]:has([data-kt-tab-toggle].active) .kt-tab-active\:border-primary\/10{
            border-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    .kt-tab-active\:bg-primary\/10[data-kt-tab-toggle].active{
        background-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-tab-active\:bg-primary\/10[data-kt-tab-toggle].active{
            background-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    [data-kt-tab-toggle].active .kt-tab-active\:bg-primary\/10{
        background-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        [data-kt-tab-toggle].active .kt-tab-active\:bg-primary\/10{
            background-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    [data-kt-tabs-initialized] [data-kt-dropdown-initialized]:has([data-kt-tab-toggle].active) .kt-tab-active\:bg-primary\/10{
        background-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        [data-kt-tabs-initialized] [data-kt-dropdown-initialized]:has([data-kt-tab-toggle].active) .kt-tab-active\:bg-primary\/10{
            background-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    .kt-tab-active\:text-primary[data-kt-tab-toggle].active,[data-kt-tab-toggle].active .kt-tab-active\:text-primary,[data-kt-tabs-initialized] [data-kt-dropdown-initialized]:has([data-kt-tab-toggle].active) .kt-tab-active\:text-primary{
        color:var(--primary)
    }
    .kt-toggle-active\:rotate-180[data-kt-toggle].active,[data-kt-toggle].active .kt-toggle-active\:rotate-180{
        rotate:180deg
    }
    .rtl\:kt-toggle-active\:rotate-0:where(:dir(rtl),[dir=rtl],[dir=rtl] *)[data-kt-toggle].active,[data-kt-toggle].active .rtl\:kt-toggle-active\:rotate-0:where(:dir(rtl),[dir=rtl],[dir=rtl] *){
        rotate:none
    }
    .kt-toggle-password-active\:block[data-kt-toggle-password-initialized].active,[data-kt-toggle-password-initialized].active .kt-toggle-password-active\:block{
        display:block
    }
    .kt-toggle-password-active\:hidden[data-kt-toggle-password-initialized].active,[data-kt-toggle-password-initialized].active .kt-toggle-password-active\:hidden,.light\:hidden:not(.dark *){
        display:none
    }
    .light\:bg-accent\/50:not(.dark *){
        background-color:var(--accent)
    }
    @supports (color:color-mix(in lab, red, red)){
        .light\:bg-accent\/50:not(.dark *){
            background-color:color-mix(in oklab,var(--accent)50%,transparent)
        }
    }
    .kt-menu-item-active\:rounded-lg.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:rounded-lg,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:rounded-lg,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:rounded-lg,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:rounded-lg{
        border-radius:var(--radius)
    }
    .kt-menu-item-active\:border-none.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:border-none,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:border-none,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:border-none,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:border-none{
        --tw-border-style:none;
        border-style:none
    }
    .kt-menu-item-active\:border-border.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:border-border,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:border-border,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:border-border,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:border-border{
        border-color:var(--border)
    }
    .kt-menu-item-active\:border-b-gray-400.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:border-b-gray-400,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:border-b-gray-400,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:border-b-gray-400,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:border-b-gray-400{
        border-bottom-color:var(--color-gray-400)
    }
    .kt-menu-item-active\:border-b-mono.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:border-b-mono,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:border-b-mono,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:border-b-mono,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:border-b-mono{
        border-bottom-color:var(--mono)
    }
    .kt-menu-item-active\:border-b-primary.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:border-b-primary,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:border-b-primary,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:border-b-primary,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:border-b-primary{
        border-bottom-color:var(--primary)
    }
    .kt-menu-item-active\:\!bg-background.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:\!bg-background,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:\!bg-background,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:\!bg-background,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:\!bg-background{
        background-color:var(--background)!important
    }
    .kt-menu-item-active\:bg-accent\/60.kt-menu-item.active{
        background-color:var(--accent)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-menu-item-active\:bg-accent\/60.kt-menu-item.active{
            background-color:color-mix(in oklab,var(--accent)60%,transparent)
        }
    }
    .kt-menu-item.active .kt-menu-item-active\:bg-accent\/60{
        background-color:var(--accent)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-menu-item.active .kt-menu-item-active\:bg-accent\/60{
            background-color:color-mix(in oklab,var(--accent)60%,transparent)
        }
    }
    .kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:bg-accent\/60{
        background-color:var(--accent)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:bg-accent\/60{
            background-color:color-mix(in oklab,var(--accent)60%,transparent)
        }
    }
    .kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:bg-accent\/60{
        background-color:var(--accent)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:bg-accent\/60{
            background-color:color-mix(in oklab,var(--accent)60%,transparent)
        }
    }
    .kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:bg-accent\/60{
        background-color:var(--accent)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:bg-accent\/60{
            background-color:color-mix(in oklab,var(--accent)60%,transparent)
        }
    }
    .kt-menu-item-active\:bg-background.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:bg-background,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:bg-background,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:bg-background,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:bg-background{
        background-color:var(--background)
    }
    .kt-menu-item-active\:bg-secondary.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:bg-secondary,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:bg-secondary,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:bg-secondary,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:bg-secondary{
        background-color:var(--secondary)
    }
    .kt-menu-item-active\:font-medium.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:font-medium,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:font-medium,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:font-medium,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:font-medium{
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium)
    }
    .kt-menu-item-active\:font-semibold.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:font-semibold,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:font-semibold,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:font-semibold,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:font-semibold{
        --tw-font-weight:var(--font-weight-semibold);
        font-weight:var(--font-weight-semibold)
    }
    .kt-menu-item-active\:text-foreground.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:text-foreground,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:text-foreground,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:text-foreground,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:text-foreground{
        color:var(--foreground)
    }
    .kt-menu-item-active\:text-mono.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:text-mono,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:text-mono,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:text-mono,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:text-mono{
        color:var(--mono)
    }
    .kt-menu-item-active\:text-primary.kt-menu-item.active,.kt-menu-item.active .kt-menu-item-active\:text-primary,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:text-primary,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:text-primary,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:text-primary{
        color:var(--primary)
    }
    .kt-menu-item-active\:before\:bg-primary.kt-menu-item.active:before,.kt-menu-item.active .kt-menu-item-active\:before\:bg-primary:before,.kt-menu-item.active>.kt-menu-link .kt-menu-item-active\:before\:bg-primary:before,.kt-menu-item.active>.kt-menu-label .kt-menu-item-active\:before\:bg-primary:before,.kt-menu-item.active>.kt-menu-toggle .kt-menu-item-active\:before\:bg-primary:before{
        content:var(--tw-content);
        background-color:var(--primary)
    }
    .kt-menu-item-here\:border-border.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:border-border,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:border-border,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:border-border,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:border-border{
        border-color:var(--border)
    }
    .kt-menu-item-here\:border-b-gray-400.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:border-b-gray-400,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:border-b-gray-400,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:border-b-gray-400,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:border-b-gray-400{
        border-bottom-color:var(--color-gray-400)
    }
    .kt-menu-item-here\:border-b-mono.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:border-b-mono,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:border-b-mono,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:border-b-mono,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:border-b-mono{
        border-bottom-color:var(--mono)
    }
    .kt-menu-item-here\:border-b-primary.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:border-b-primary,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:border-b-primary,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:border-b-primary,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:border-b-primary{
        border-bottom-color:var(--primary)
    }
    .kt-menu-item-here\:bg-background.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:bg-background,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:bg-background,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:bg-background,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:bg-background{
        background-color:var(--background)
    }
    .kt-menu-item-here\:bg-transparent.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:bg-transparent,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:bg-transparent,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:bg-transparent,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:bg-transparent{
        background-color:#0000
    }
    .kt-menu-item-here\:font-medium.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:font-medium,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:font-medium,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:font-medium,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:font-medium{
        --tw-font-weight:var(--font-weight-medium);
        font-weight:var(--font-weight-medium)
    }
    .kt-menu-item-here\:font-semibold.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:font-semibold,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:font-semibold,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:font-semibold,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:font-semibold{
        --tw-font-weight:var(--font-weight-semibold);
        font-weight:var(--font-weight-semibold)
    }
    .kt-menu-item-here\:text-foreground.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:text-foreground,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:text-foreground,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:text-foreground,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:text-foreground{
        color:var(--foreground)
    }
    .kt-menu-item-here\:text-mono.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:text-mono,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:text-mono,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:text-mono,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:text-mono{
        color:var(--mono)
    }
    .kt-menu-item-here\:text-muted-foreground.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:text-muted-foreground,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:text-muted-foreground,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:text-muted-foreground,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:text-muted-foreground{
        color:var(--muted-foreground)
    }
    .kt-menu-item-here\:text-primary.kt-menu-item.here,.kt-menu-item.here .kt-menu-item-here\:text-primary,.kt-menu-item.here>.kt-menu-link .kt-menu-item-here\:text-primary,.kt-menu-item.here>.kt-menu-label .kt-menu-item-here\:text-primary,.kt-menu-item.here>.kt-menu-toggle .kt-menu-item-here\:text-primary{
        color:var(--primary)
    }
    .kt-menu-item-show\:\!flex.kt-menu-item.show,.kt-menu-item.show>.kt-menu-link .kt-menu-item-show\:\!flex,.kt-menu-item.show>.kt-menu-label .kt-menu-item-show\:\!flex,.kt-menu-item.show>.kt-menu-toggle .kt-menu-item-show\:\!flex{
        display:flex!important
    }
    .kt-menu-item-show\:hidden.kt-menu-item.show,.kt-menu-item.show>.kt-menu-link .kt-menu-item-show\:hidden,.kt-menu-item.show>.kt-menu-label .kt-menu-item-show\:hidden,.kt-menu-item.show>.kt-menu-toggle .kt-menu-item-show\:hidden{
        display:none
    }
    .kt-menu-item-show\:inline-flex.kt-menu-item.show,.kt-menu-item.show>.kt-menu-link .kt-menu-item-show\:inline-flex,.kt-menu-item.show>.kt-menu-label .kt-menu-item-show\:inline-flex,.kt-menu-item.show>.kt-menu-toggle .kt-menu-item-show\:inline-flex{
        display:inline-flex
    }
    .kt-menu-item-show\:text-foreground.kt-menu-item.show,.kt-menu-item.show>.kt-menu-link .kt-menu-item-show\:text-foreground,.kt-menu-item.show>.kt-menu-label .kt-menu-item-show\:text-foreground,.kt-menu-item.show>.kt-menu-toggle .kt-menu-item-show\:text-foreground{
        color:var(--foreground)
    }
    .kt-menu-item-show\:text-mono.kt-menu-item.show,.kt-menu-item.show>.kt-menu-link .kt-menu-item-show\:text-mono,.kt-menu-item.show>.kt-menu-label .kt-menu-item-show\:text-mono,.kt-menu-item.show>.kt-menu-toggle .kt-menu-item-show\:text-mono{
        color:var(--mono)
    }
    .kt-menu-item-show\:text-primary.kt-menu-item.show,.kt-menu-item.show>.kt-menu-link .kt-menu-item-show\:text-primary,.kt-menu-item.show>.kt-menu-label .kt-menu-item-show\:text-primary,.kt-menu-item.show>.kt-menu-toggle .kt-menu-item-show\:text-primary{
        color:var(--primary)
    }
    .kt-menu-link-hover\:border-none.kt-menu-link:hover,.kt-menu-link:hover .kt-menu-link-hover\:border-none{
        --tw-border-style:none;
        border-style:none
    }
    .kt-menu-link-hover\:border-border.kt-menu-link:hover,.kt-menu-link:hover .kt-menu-link-hover\:border-border{
        border-color:var(--border)
    }
    .kt-menu-link-hover\:\!bg-background.kt-menu-link:hover,.kt-menu-link:hover .kt-menu-link-hover\:\!bg-background{
        background-color:var(--background)!important
    }
    .kt-menu-link-hover\:bg-accent\/60.kt-menu-link:hover{
        background-color:var(--accent)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-menu-link-hover\:bg-accent\/60.kt-menu-link:hover{
            background-color:color-mix(in oklab,var(--accent)60%,transparent)
        }
    }
    .kt-menu-link:hover .kt-menu-link-hover\:bg-accent\/60{
        background-color:var(--accent)
    }
    @supports (color:color-mix(in lab, red, red)){
        .kt-menu-link:hover .kt-menu-link-hover\:bg-accent\/60{
            background-color:color-mix(in oklab,var(--accent)60%,transparent)
        }
    }
    .kt-menu-link-hover\:bg-background.kt-menu-link:hover,.kt-menu-link:hover .kt-menu-link-hover\:bg-background{
        background-color:var(--background)
    }
    .kt-menu-link-hover\:bg-secondary.kt-menu-link:hover,.kt-menu-link:hover .kt-menu-link-hover\:bg-secondary{
        background-color:var(--secondary)
    }
    .kt-menu-link-hover\:\!text-primary.kt-menu-link:hover,.kt-menu-link:hover .kt-menu-link-hover\:\!text-primary{
        color:var(--primary)!important
    }
    .kt-menu-link-hover\:text-foreground.kt-menu-link:hover,.kt-menu-link:hover .kt-menu-link-hover\:text-foreground{
        color:var(--foreground)
    }
    .kt-menu-link-hover\:text-mono.kt-menu-link:hover,.kt-menu-link:hover .kt-menu-link-hover\:text-mono{
        color:var(--mono)
    }
    .kt-menu-link-hover\:text-primary.kt-menu-link:hover,.kt-menu-link:hover .kt-menu-link-hover\:text-primary{
        color:var(--primary)
    }
    .has-checked\:\[\&_\.checked\]\:flex:has(:checked) .checked{
        display:flex
    }
    @media (min-width:64rem){
        .lg\:\[\&_\.kt-container-fluid\]\:pe-4 .kt-container-fluid{
            padding-inline-end:calc(var(--spacing)*4)
        }
    }
    .\[\&_\.kt-step-icon\]\:text-muted-foreground .kt-step-icon{
        color:var(--muted-foreground)
    }
    .\[\&_\.kt-step-icon\]\:text-primary .kt-step-icon{
        color:var(--primary)
    }
    .\[\&_i\]\:text-white i{
        color:var(--color-white)
    }
    @media (hover:hover){
        .hover\:\[\&_i\]\:text-black\/80:hover i{
            color:#000c
        }
        @supports (color:color-mix(in lab, red, red)){
            .hover\:\[\&_i\]\:text-black\/80:hover i{
                color:color-mix(in oklab,var(--color-black)80%,transparent)
            }
        }
        .hover\:\[\&_i\]\:text-primary:hover i{
            color:var(--primary)
        }
    }
    [data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle].kt-dropdown-open\:\[\&_i\]\:text-primary i,[data-kt-dropdown-initialized].open>[data-kt-dropdown-toggle] .kt-dropdown-open\:\[\&_i\]\:text-primary i{
        color:var(--primary)
    }
    .\[\&_border\]\:\[\&_i\]\:text-black\/80 border i{
        color:#000c
    }
    @supports (color:color-mix(in lab, red, red)){
        .\[\&_border\]\:\[\&_i\]\:text-black\/80 border i{
            color:color-mix(in oklab,var(--color-black)80%,transparent)
        }
    }
    .ltr\:\[\&_tr\:nth-of-type\(12\)\>td\:first-child\]\:rounded-bl-xl:where(:dir(ltr),[dir=ltr],[dir=ltr] *) tr:nth-of-type(12)>td:first-child{
        border-bottom-left-radius:calc(var(--radius) + 4px)
    }
    .rtl\:\[\&_tr\:nth-of-type\(12\)\>td\:first-child\]\:rounded-br-xl:where(:dir(rtl),[dir=rtl],[dir=rtl] *) tr:nth-of-type(12)>td:first-child,.ltr\:\[\&_tr\:nth-of-type\(12\)\>td\:last-child\]\:rounded-br-xl:where(:dir(ltr),[dir=ltr],[dir=ltr] *) tr:nth-of-type(12)>td:last-child{
        border-bottom-right-radius:calc(var(--radius) + 4px)
    }
    .rtl\:\[\&_tr\:nth-of-type\(12\)\>td\:last-child\]\:rounded-bl-xl:where(:dir(rtl),[dir=rtl],[dir=rtl] *) tr:nth-of-type(12)>td:last-child{
        border-bottom-left-radius:calc(var(--radius) + 4px)
    }
    .\[\&_tr\:nth-of-type\(2\)\>td\]\:border-t tr:nth-of-type(2)>td{
        border-top-style:var(--tw-border-style);
        border-top-width:1px
    }
    .ltr\:\[\&_tr\:nth-of-type\(2\)\>td\:first-child\]\:rounded-tl-xl:where(:dir(ltr),[dir=ltr],[dir=ltr] *) tr:nth-of-type(2)>td:first-child{
        border-top-left-radius:calc(var(--radius) + 4px)
    }
    .rtl\:\[\&_tr\:nth-of-type\(2\)\>td\:first-child\]\:rounded-tr-xl:where(:dir(rtl),[dir=rtl],[dir=rtl] *) tr:nth-of-type(2)>td:first-child{
        border-top-right-radius:calc(var(--radius) + 4px)
    }
    .\[\&\.active\]\:bg-primary\/10.active{
        background-color:var(--primary)
    }
    @supports (color:color-mix(in lab, red, red)){
        .\[\&\.active\]\:bg-primary\/10.active{
            background-color:color-mix(in oklab,var(--primary)10%,transparent)
        }
    }
    .\[\&\.active\]\:\[\&_i\]\:text-primary.active i{
        color:var(--primary)
    }
    .active.\[\.active\&\]\:border-border{
        border-color:var(--border)
    }
    .active.\[\.active\&\]\:border-input{
        border-color:var(--input)
    }
    .active.\[\.active\&\]\:bg-background{
        background-color:var(--background)
    }
    .active.\[\.active\&\]\:\[\&_i\]\:text-primary i{
        color:var(--primary)
    }
}
:root{
    --background:oklch(1 0 0);
    --foreground:oklch(27.4% .006 286.033);
    --card:oklch(1 0 0);
    --card-foreground:oklch(27.4% .006 286.033);
    --popover:oklch(1 0 0);
    --popover-foreground:oklch(27.4% .006 286.033);
    --primary:#1379f0;
    --primary-foreground:oklch(1 0 0);
    --secondary:oklch(96.7% .003 264.542);
    --secondary-foreground:oklch(44.6% .03 256.802);
    --muted:oklch(96.7% .003 264.542);
    --muted-foreground:oklch(70.5% .015 286.067);
    --accent:oklch(96.7% .003 264.542);
    --accent-foreground:oklch(21% .006 285.885);
    --destructive:oklch(57.7% .245 27.325);
    --destructive-foreground:oklch(1 0 0);
    --mono:oklch(14.1% .005 285.823);
    --mono-foreground:oklch(1 0 0);
    --border:oklch(94% .004 286.32);
    --input:oklch(92% .004 286.32);
    --ring:oklch(87.1% .006 286.286);
    --radius:.5rem
}
/* .dark{
    --background:oklch(14.1% .005 285.823);
    --foreground:oklch(98.5% 0 0);
    --card:oklch(14.1% .005 285.823);
    --card-foreground:oklch(98.5% 0 0);
    --popover:oklch(14.1% .005 285.823);
    --popover-foreground:oklch(98.5% 0 0);
    --primary:#1379f0;
    --primary-foreground:oklch(1 0 0);
    --secondary:oklch(27.4% .006 286.033);
    --secondary-foreground:oklch(70.5% .015 286.067);
    --muted:oklch(21% .006 285.885);
    --muted-foreground:oklch(55.2% .016 285.938);
    --accent:oklch(21% .006 285.885);
    --accent-foreground:oklch(98.5% 0 0);
    --destructive:oklch(57.7% .245 27.325);
    --destructive-foreground:oklch(1 0 0);
    --mono:oklch(87.1% .006 286.286);
    --mono-foreground:oklch(0 0 0);
    --border:oklch(27.4% .006 286.033);
    --input:oklch(27.4% .006 286.033);
    --ring:oklch(27.4% .006 286.033)
} */
.dark{
    --background:#1a2035;
    --foreground:oklch(0.82 0 0);
    --card:#171e36;
    --card-foreground:oklch(98.5% 0 0);
    --popover:#1a2035;
    --popover-foreground:oklch(0.82 0 0);
    --primary:#1379f0;
    --primary-foreground:oklch(1 0 0);
    --secondary:oklch(27.4% .006 286.033);
    --secondary-foreground:oklch(70.5% .015 286.067);
    --muted:oklch(21% .006 285.885);
    --muted-foreground:oklch(55.2% .016 285.938);
    --accent:oklch(21% .006 285.885);
    --accent-foreground:oklch(0.82 0 0);
    --destructive:oklch(57.7% .245 27.325);
    --destructive-foreground:oklch(1 0 0);
    --mono:oklch(87.1% .006 286.286);
    --mono-foreground:oklch(0 0 0);
    --border:oklch(0.22 0.07 263.63);
    --input:oklch(0.33 0 0);
    --ring:oklch(27.4% .006 286.033)
}
.appearance-none{
    box-shadow:none;
    background-color:#0000;
    border:0;
    width:0;
    height:0;
    background-image:none!important
}
@property --tw-border-spacing-x{
    syntax:"<length>";
    inherits:false;
    initial-value:0
}
@property --tw-border-spacing-y{
    syntax:"<length>";
    inherits:false;
    initial-value:0
}
@property --tw-translate-x{
    syntax:"*";
    inherits:false;
    initial-value:0
}
@property --tw-translate-y{
    syntax:"*";
    inherits:false;
    initial-value:0
}
@property --tw-translate-z{
    syntax:"*";
    inherits:false;
    initial-value:0
}
@property --tw-rotate-x{
    syntax:"*";
    inherits:false
}
@property --tw-rotate-y{
    syntax:"*";
    inherits:false
}
@property --tw-rotate-z{
    syntax:"*";
    inherits:false
}
@property --tw-skew-x{
    syntax:"*";
    inherits:false
}
@property --tw-skew-y{
    syntax:"*";
    inherits:false
}
@property --tw-space-y-reverse{
    syntax:"*";
    inherits:false;
    initial-value:0
}
@property --tw-space-x-reverse{
    syntax:"*";
    inherits:false;
    initial-value:0
}
@property --tw-divide-y-reverse{
    syntax:"*";
    inherits:false;
    initial-value:0
}
@property --tw-border-style{
    syntax:"*";
    inherits:false;
    initial-value:solid
}
@property --tw-gradient-position{
    syntax:"*";
    inherits:false
}
@property --tw-gradient-from{
    syntax:"<color>";
    inherits:false;
    initial-value:#0000
}
@property --tw-gradient-via{
    syntax:"<color>";
    inherits:false;
    initial-value:#0000
}
@property --tw-gradient-to{
    syntax:"<color>";
    inherits:false;
    initial-value:#0000
}
@property --tw-gradient-stops{
    syntax:"*";
    inherits:false
}
@property --tw-gradient-via-stops{
    syntax:"*";
    inherits:false
}
@property --tw-gradient-from-position{
    syntax:"<length-percentage>";
    inherits:false;
    initial-value:0%
}
@property --tw-gradient-via-position{
    syntax:"<length-percentage>";
    inherits:false;
    initial-value:50%
}
@property --tw-gradient-to-position{
    syntax:"<length-percentage>";
    inherits:false;
    initial-value:100%
}
@property --tw-leading{
    syntax:"*";
    inherits:false
}
@property --tw-font-weight{
    syntax:"*";
    inherits:false
}
@property --tw-tracking{
    syntax:"*";
    inherits:false
}
@property --tw-shadow{
    syntax:"*";
    inherits:false;
    initial-value:0 0 #0000
}
@property --tw-shadow-color{
    syntax:"*";
    inherits:false
}
@property --tw-shadow-alpha{
    syntax:"<percentage>";
    inherits:false;
    initial-value:100%
}
@property --tw-inset-shadow{
    syntax:"*";
    inherits:false;
    initial-value:0 0 #0000
}
@property --tw-inset-shadow-color{
    syntax:"*";
    inherits:false
}
@property --tw-inset-shadow-alpha{
    syntax:"<percentage>";
    inherits:false;
    initial-value:100%
}
@property --tw-ring-color{
    syntax:"*";
    inherits:false
}
@property --tw-ring-shadow{
    syntax:"*";
    inherits:false;
    initial-value:0 0 #0000
}
@property --tw-inset-ring-color{
    syntax:"*";
    inherits:false
}
@property --tw-inset-ring-shadow{
    syntax:"*";
    inherits:false;
    initial-value:0 0 #0000
}
@property --tw-ring-inset{
    syntax:"*";
    inherits:false
}
@property --tw-ring-offset-width{
    syntax:"<length>";
    inherits:false;
    initial-value:0
}
@property --tw-ring-offset-color{
    syntax:"*";
    inherits:false;
    initial-value:#fff
}
@property --tw-ring-offset-shadow{
    syntax:"*";
    inherits:false;
    initial-value:0 0 #0000
}
@property --tw-outline-style{
    syntax:"*";
    inherits:false;
    initial-value:solid
}
@property --tw-blur{
    syntax:"*";
    inherits:false
}
@property --tw-brightness{
    syntax:"*";
    inherits:false
}
@property --tw-contrast{
    syntax:"*";
    inherits:false
}
@property --tw-grayscale{
    syntax:"*";
    inherits:false
}
@property --tw-hue-rotate{
    syntax:"*";
    inherits:false
}
@property --tw-invert{
    syntax:"*";
    inherits:false
}
@property --tw-opacity{
    syntax:"*";
    inherits:false
}
@property --tw-saturate{
    syntax:"*";
    inherits:false
}
@property --tw-sepia{
    syntax:"*";
    inherits:false
}
@property --tw-drop-shadow{
    syntax:"*";
    inherits:false
}
@property --tw-drop-shadow-color{
    syntax:"*";
    inherits:false
}
@property --tw-drop-shadow-alpha{
    syntax:"<percentage>";
    inherits:false;
    initial-value:100%
}
@property --tw-drop-shadow-size{
    syntax:"*";
    inherits:false
}
@property --tw-backdrop-blur{
    syntax:"*";
    inherits:false
}
@property --tw-backdrop-brightness{
    syntax:"*";
    inherits:false
}
@property --tw-backdrop-contrast{
    syntax:"*";
    inherits:false
}
@property --tw-backdrop-grayscale{
    syntax:"*";
    inherits:false
}
@property --tw-backdrop-hue-rotate{
    syntax:"*";
    inherits:false
}
@property --tw-backdrop-invert{
    syntax:"*";
    inherits:false
}
@property --tw-backdrop-opacity{
    syntax:"*";
    inherits:false
}
@property --tw-backdrop-saturate{
    syntax:"*";
    inherits:false
}
@property --tw-backdrop-sepia{
    syntax:"*";
    inherits:false
}
@property --tw-duration{
    syntax:"*";
    inherits:false
}
@property --tw-content{
    syntax:"*";
    inherits:false;
    initial-value:""
}
@property --tw-ease{
    syntax:"*";
    inherits:false
}
@keyframes spin{
    to{
        transform:rotate(360deg)
    }
}
@keyframes pulse{
    50%{
        opacity:.5
    }
}

<?php

namespace App\Library;

class ZteAPI
{
    protected string $host;
    protected string $community;
    protected int $port;

    public function __construct(string $host, string $community = 'public', int $port = 2161)
    {
        $this->host = $host;
        $this->community = $community;
        $this->port = $port;
    }

    protected function snmpGet(string $oidName): ?string
    {
        $cmd = sprintf('snmpget -v2c -c %s -m ALL %s:%d %s', $this->community, $this->host, $this->port, $oidName);
        $output = shell_exec($cmd);
        return $this->parseSnmpValue($output);
    }

    protected function snmpWalk(string $oidName): array
    {
        $cmd = sprintf('snmpwalk -v2c -c %s -m ALL %s:%d %s', $this->community, $this->host, $this->port, $oidName);
        $output = shell_exec($cmd);
        return $output ? array_filter(explode("\n", $output)) : [];
    }

    protected function parseSnmpValue(?string $output): ?string
    {
        if (!$output) return null;
        if (strpos($output, '=') !== false) {
            [$left, $right] = explode('=', $output, 2);
            $right = trim($right);
            if (strpos($right, ':') !== false) {
                [, $value] = explode(':', $right, 2);
                return trim($value);
            }
            return $right;
        }
        return trim($output);
    }

    public function getSysName(): ?string
    {
        return $this->snmpGet('SNMPv2-MIB::sysName.0');
    }

    public function getSysUptime(): ?string
    {
        return $this->snmpGet('SNMPv2-MIB::sysUpTime.0');
    }

    public function getCpuUsage(): ?string
    {
        return $this->snmpGet('ZXCESPERFORMANCE-MIB::zxCpuUsage.0');
    }

    public function getMemoryUsage(): ?string
    {
        return $this->snmpGet('ZXCESPERFORMANCE-MIB::zxMemUsage.0');
    }

    public function getOnuStatusList(): array
    {
        $lines = $this->snmpWalk('ZXGPON-ONTMGMT-MIB::zxGponONTOperState');
        $data = [];
        foreach ($lines as $line) {
            if (preg_match('/\.(\d+)\.(\d+)\s+=\s+INTEGER:\s+(\d+)/', $line, $m)) {
                $data[] = [
                    'pon_index' => (int) $m[1],
                    'onu_index' => (int) $m[2],
                    'status' => $m[3] === '1' ? 'online' : 'offline',
                ];
            }
        }
        return $data;
    }

    public function getOnuList(): array
    {
        $fields = [
            'serial' => 'ZXGPON-ONTMGMT-MIB::zxGponONTSerialNum',
            'status' => 'ZXGPON-ONTMGMT-MIB::zxGponONTOperState',
            'rx_power' => 'ZXAN-TRANSCEIVER-MIB::zxAnRxOpticalPower',
            'tx_power' => 'ZXAN-TRANSCEIVER-MIB::zxAnTxOpticalPower',
        ];

        $tmp = [];
        foreach ($fields as $key => $oid) {
            $lines = $this->snmpWalk($oid);
            foreach ($lines as $line) {
                if (preg_match('/\.(\d+)\.(\d+)\s+=\s+[^:]+:\s+(.+)/', $line, $m)) {
                    $tmp[$m[1]][$m[2]][$key] = trim($m[3]);
                }
            }
        }

        $list = [];
        foreach ($tmp as $pon => $onus) {
            foreach ($onus as $onu => $d) {
                $list[] = [
                    'pon_index' => (int) $pon,
                    'onu_index' => (int) $onu,
                    'serial'    => $d['serial'] ?? null,
                    'status'    => ($d['status'] ?? '2') === '1' ? 'online' : 'offline',
                    'rx_power'  => $d['rx_power'] ?? null,
                    'tx_power'  => $d['tx_power'] ?? null,
                ];
            }
        }
        return $list;
    }
}
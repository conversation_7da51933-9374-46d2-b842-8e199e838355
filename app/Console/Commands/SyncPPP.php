<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Mikrotik\Nas;
use App\Models\Radius\RadiusSession;
use Illuminate\Support\Facades\Cache;
use Spatie\Fork\Fork;

class SyncPPP extends Command
{
    protected $signature = 'nas:sync-ppp';
    protected $description = 'Ping semua NAS dan hentikan sesi PPP jika gagal ping 5x berturut-turut dan offline >10 menit';

    public function handle()
    {
        $this->info('📡 Mengecek status NAS via ping...');

        $nasList = Nas::all();

        Fork::new()
            ->run(
                ...$nasList->map(function ($nas) {
                    return function () use ($nas) {
                        $ip = $nas->ip_router;

                        $failCountKey = "ppp_nas_fail_count:$ip";
                        $cacheStatus = "ppp_nas_status:$ip";
                        $cacheOfflineSince = "ppp_nas_offline_since:$ip";

                        // Ping: kirim 5 paket dengan timeout 1 detik
                        exec("ping -c 5 -W 1 $ip", $output, $status);

                        if ($status === 0) {
                            // Jika ping sukses, reset semua indikator offline
                            Cache::store('redis')->forget($failCountKey);
                            Cache::store('redis')->put($cacheStatus, 'online', 600);
                            Cache::store('redis')->forget($cacheOfflineSince);
                            echo "🟢 ONLINE: {$nas->shortname} ($ip)\n";
                        } else {
                            // Jika ping gagal, tambahkan fail count
                            $failCount = Cache::store('redis')->increment($failCountKey);
                            Cache::store('redis')->put($failCountKey, $failCount, 1800); // TTL 30 menit

                            if ($failCount >= 5) {
                                // Setelah 5x berturut-turut gagal, dianggap offline
                                Cache::store('redis')->put($cacheStatus, 'offline', 600);
                                if (!Cache::store('redis')->has($cacheOfflineSince)) {
                                    Cache::store('redis')->put($cacheOfflineSince, now()->timestamp, 3600);
                                }
                                echo "🔴 OFFLINE: {$nas->shortname} ($ip) (gagal ping $failCountx)\n";
                            } else {
                                echo "⚠️  Ping gagal ke {$nas->shortname} ($ip) → ke-$failCount\n";
                            }
                        }
                    };
                })->toArray()
            );

        $this->info('🛑 Menyinkronkan sesi PPP dari NAS yang offline > 10 menit...');

        $totalUpdated = 0;

        foreach ($nasList as $nas) {
            $ip = $nas->ip_router;
            $status = Cache::store('redis')->get("ppp_nas_status:$ip");
            $offlineSince = Cache::store('redis')->get("ppp_nas_offline_since:$ip");

            if ($status === 'offline' && $offlineSince) {
                $offlineDuration = now()->timestamp - intval($offlineSince);

                if ($offlineDuration >= 600) { // 10 menit
                    $affected = RadiusSession::where('status', 1)
                        ->where('type', 2)
                        ->where('nas_address', $ip)
                        ->update([
                            'status' => 2,
                            'stop' => now(),
                            'update' => now(),
                        ]);

                    if ($affected > 0) {
                        \Log::info("❌ {$nas->shortname} {$nas->ip_router} {$nas->name} OFFLINE > 10 menit → $affected sesi PPP disetop.");
                        $totalUpdated += $affected;
                    }
                } else {
                    $this->line("⏳ {$nas->shortname} OFFLINE < 10 menit → skip stop sesi.");
                }
            }
        }

        $this->info("✅ Selesai. Total sesi PPP yang disetop karena NAS offline > 10 menit: $totalUpdated");
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Setting\BillingSetting;
use App\Models\Whatsapp\Mpwa;
use App\Models\Whatsapp\Watemplate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Invoice\Invoice;
use Spatie\Fork\Fork;

class GenerateSingleBillingInvoice extends Command
{
    protected $signature = 'invoice:single {user_id} {tanggal}';
    protected $description = 'Generate invoice untuk satu user Admin';

    public function handle()
    {
        $userId = $this->argument('user_id');
        $tanggal = Carbon::parse($this->argument('tanggal'));

        $user = User::with('c_pppoe_cycle.c_profile')->find($userId);
        if (! $user) {
            $this->error("User ID {$userId} tidak ditemukan.");
            return 1;
        }

        // Panggil kembali method private dengan membuat instance
        (new \App\Console\Commands\GenerateBillingCycleInvoice())->handleUser($user, $tanggal);
        return 0;
    }
}

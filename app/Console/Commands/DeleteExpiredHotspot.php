<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Hotspot\HotspotUser;

class DeleteExpiredHotspot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hotspot:delete-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete hotspot user dengan status 3 dan end_time lebih dari 2 bulan';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        set_time_limit(0);

        $cutoff = Carbon::now()->subMonths(1);

        // Ambil semua user admin aktif
        $users = User::where('role', 'Admin')->whereIn('status', [1, 3])->get();
        $totalDeleted = 0;

        foreach ($users as $user) {
            $deleted = HotspotUser::where('shortname', $user->shortname)
                ->where('status', 3)
                ->where('end_time', '<', $cutoff)
                ->delete();

            $this->info("{$user->shortname}: Deleted {$deleted} expired hotspot user(s)");
            $totalDeleted += $deleted;
        }

        \Log::info("✅ Total deleted: {$totalDeleted} user(s) expired lebih dari 2 bulan.");
    }
}

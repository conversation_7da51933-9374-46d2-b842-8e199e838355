<?php

namespace App\Exports;

use App\Models\Keuangan\Transaksi;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Events\AfterSheet;
use Carbon\Carbon;

class TransaksiExport implements FromCollection, WithHeadings, WithStyles,ShouldAutoSize, WithMapping, WithColumnFormatting, WithEvents
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function collection()
{
    $query = Transaksi::query()->where('shortname', multi_auth()->shortname);

    // Filter tanggal (periode) dalam format "d/m/Y - d/m/Y"
    if ($this->request->filled('periode')) {
        try {
            if (str_contains($this->request->periode, ' - ')) {
                [$start, $end] = explode(' - ', $this->request->periode);
            } else {
                $start = $end = $this->request->periode;
            }

            $start = \Carbon\Carbon::createFromFormat('d/m/Y', trim($start))->startOfDay();
            $end = \Carbon\Carbon::createFromFormat('d/m/Y', trim($end))->endOfDay();

            $query->whereBetween('tanggal', [$start, $end]);
        } catch (\Exception $e) {
            // Jika gagal parsing, biarkan tanpa filter
        }
    }

    if ($this->request->filled('created_by')) {
        $query->where('created_by', $this->request->created_by);
    }

    if ($this->request->filled('reseller')) {
        $query->where('reseller', $this->request->reseller);
    }

    if ($this->request->filled('tipe')) {
        $query->where('tipe', $this->request->tipe);
    }

    if ($this->request->filled('kategori')) {
        $query->where('kategori', $this->request->kategori);
    }

    if ($this->request->filled('nas')) {
        $query->where('nas', $this->request->nas);
    }

    // Exclude created_by = frradius
    $query->where('created_by', '!=', 'frradius');

    return $query->get();
}


    public function headings(): array
    {
        return ['Tanggal', 'Tipe', 'Kategori', 'Deskripsi','Nominal','Metode','NAS','Reseller','Created By'];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]], // Bold untuk header
        ];
    }

    public function map($row): array
    {
        // Ubah field tanggal menjadi format d-m-Y tanpa waktu
        $tanggal = Carbon::parse($row->tanggal)->format('d/m/Y');

        return [
            $tanggal,
            $row->tipe,
            $row->kategori,
            $row->deskripsi,
            $row->nominal,
            $row->metode,
            \App\Models\Mikrotik\Nas::where('ip_router', $row->nas)->first()->name ?? 'ALL',
            $row->reseller,
            $row->created_by,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'E' => '#,##0_-', // Format Rupiah tanpa koma desimal
        ];
    }
    
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Mengatur header jadi bold dan rata tengah
                $event->sheet->getStyle('A1:G1')->applyFromArray([
                    'font' => ['bold' => true],
                    'alignment' => ['horizontal' => 'center'],
                ]);
                
                // Mengatur lebar kolom otomatis
                foreach (range('A', 'G') as $col) {
                    $event->sheet->getColumnDimension($col)->setAutoSize(true);
                }
            },
        ];
    }
}

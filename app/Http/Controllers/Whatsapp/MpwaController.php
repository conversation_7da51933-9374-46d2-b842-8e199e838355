<?php

namespace App\Http\Controllers\Whatsapp;

use App\Http\Controllers\Controller;
use App\Models\Whatsapp\Watemplate;
use Illuminate\Http\Request;
use App\Models\Whatsapp\Mpwa;
use Illuminate\Support\Str;
use App\Models\Mapping\Pop;
use App\Models\Mapping\Odp;
use App\Models\Pppoe\PppoeUser;
use App\Models\Partnership\Mitra;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Setting\WaServer;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class MpwaController extends Controller
{
    public function daftar(Request $request)
    {
        $wa = WaServer::where('wa_url', $request->mpwa_server)->first();
        try {
            $curl = curl_init();
            $data = [
                'api_key' => $wa->wa_api,
                'username' => multi_auth()->shortname,
                'password' => '12345678',
                'email' => multi_auth()?->email ?? 'user' . rand(100000, 999999) . '@frradius.com',
                'expire' => 365,
                'limit_device' => 1,
            ];
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_URL, 'https://' . $request->mpwa_server . '/create-user');
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);

            $response = curl_exec($curl);
            curl_close($curl);
            $result = json_decode($response, true);

            $user = Mpwa::create([
                'shortname' => multi_auth()->shortname,
                'mpwa_server' => $request->mpwa_server,
                'sender' => $request->no_wa_daftar,
                'user_id' => $result['user_id'],
                'api_key' => $result['api_key'],
                'webhook' => '',
            ]);

            $curl = curl_init();
            $data = [
                'api_key' => $user->api_key,
                'device' => $user->sender,
                'force' => true,
            ];
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_URL, 'https://' . $request->mpwa_server . '/generate-qr');
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
            $response = curl_exec($curl);
            curl_close($curl);
            // $result = json_decode($response, true);
        } catch (\Exception $e) {
            return $e->getMessage();
        }

        return response()->json([
            'success' => false,
            'message' => 'Daftar WA Gateway Berhasil, Silakan Scan Device',
        ]);
    }
    public function index()
    {
        $wa_server = WaServer::get();
        $areas = Pop::where('shortname', multi_auth()->shortname)->orderBy('kode_area', 'desc')->get();
        $odps = Odp::where('shortname', multi_auth()->shortname)->orderBy('kode_odp', 'desc')->get();
        $mitras = Mitra::where('shortname', multi_auth()->shortname)->get();
        $statuses = PppoeUser::where('shortname', multi_auth()->shortname)->get();
        $whatsapp = Mpwa::where('shortname', multi_auth()->shortname)->first();

        if ($whatsapp == null) {
            return view('backend.whatsapp.register', compact('wa_server'));
        } else {
            try {
                $curl = curl_init();
                $data = [
                    'api_key' => $whatsapp->api_key,
                    'number' => $whatsapp->sender,
                ];
                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
                curl_setopt($curl, CURLOPT_URL, 'https://' . $whatsapp->mpwa_server . '/info-device');
                curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
                curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
                $response = curl_exec($curl);
                curl_close($curl);
                $result = json_decode($response, true);
                // dd($response);
                if (request()->ajax()) {
                    $curl2 = curl_init();
                    $data2 = [
                        'api_key' => $whatsapp->api_key,
                        'user_id' => $whatsapp->user_id,
                    ];
                    curl_setopt($curl2, CURLOPT_CUSTOMREQUEST, 'POST');
                    curl_setopt($curl2, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($curl2, CURLOPT_POSTFIELDS, http_build_query($data2));
                    curl_setopt($curl2, CURLOPT_URL, 'https://' . $whatsapp->mpwa_server . '/get-messages');
                    curl_setopt($curl2, CURLOPT_SSL_VERIFYHOST, 0);
                    curl_setopt($curl2, CURLOPT_SSL_VERIFYPEER, 0);
                    $response2 = curl_exec($curl2);
                    curl_close($curl2);
                    $result2 = json_decode($response2, true);
                    $messages = collect($result2['message']);
                    $selectedIds = request()->get('idsel') ?? [];
                    return DataTables::of($messages)
                        ->addColumn('checkbox', function ($row) use ($selectedIds) {
                            $checked = in_array($row['id'], $selectedIds) ? ' checked' : ''; // Periksa apakah ID ada dalam array
                            return '<input type="checkbox" class="row-cb form-check-input kt-checkbox kt-checkbox-sm" id="checkbox_row' . $row['id'] . '" value="' . $row['id'] . '"' . $checked . ' />';
                        })
                        ->rawColumns(['action', 'checkbox'])
                        ->toJson();
                }

                return view('backend.whatsapp.index', compact('result', 'areas', 'odps', 'mitras', 'statuses', 'whatsapp'));
            } catch (\Exception $e) {
                return $e->getMessage();
            }
        }
    }

    public function scan(Request $request)
    {
        $user = Mpwa::where('shortname', multi_auth()->shortname)->first();
        try {
            $curl = curl_init();
            $data = [
                'api_key' => $user->api_key,
                'device' => $user->sender,
                'force' => true,
            ];
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_URL, 'https://' . $user->mpwa_server . '/generate-qr');
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
            $response = curl_exec($curl);
            curl_close($curl);
            $result = json_decode($response, true);
            // dd($result);
            if (!isset($result['qrcode'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Generate QR gagal, silakan coba lagi',
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'message' => 'Generate QR berhasil, silakan scan device',
                    'data' => $result['qrcode'],
                ]);
            }
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function destroy(Request $request)
    {
        // $message = WablasMessage::whereIn('id', $request->ids)->delete();
        // return response()->json([
        //     'success' => true,
        //     'message' => 'Data Berhasil Dihapus',
        // ]);
    }
    public function show()
    {
        // $wablas = Wablas::where('group_id', multi_auth()->shortname)->get();
        // return response()->json([
        //     'success' => true,
        //     'data' => $wablas,
        // ]);
    }
    public function update(Request $request, Mpwa $whatsapp)
    {
        $validator = Validator::make($request->all(), [
            'sender' => 'required|string|min:8|max:15|unique:mpwa.setting',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors(),
            ]);
        }

        $user = Mpwa::where('shortname', multi_auth()->shortname)->first();
        try {
            $curl = curl_init();
            $data = [
                'api_key' => $user->api_key,
                'device' => $request->no_wa,
                'force' => true,
            ];
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_URL, 'https://' . $user->mpwa_server . '/generate-qr');
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
            $response = curl_exec($curl);
            curl_close($curl);
            $result = json_decode($response, true);
            if (!isset($result['status'])) {
                return response()->json([
                    'success' => true,
                    'message' => 'Ganti nomor gagal, silakan coba lagi',
                ]);
            } else {
                $whatsapp->update([
                    'sender' => $request->no_wa,
                ]);
                return response()->json([
                    'success' => true,
                    'message' => 'Ganti nomor berhasil, silakan scan device',
                ]);
            }
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function getTemplate(Request $request)
    {
        $data = Watemplate::where('shortname', multi_auth()->shortname)->first();
        return response()->json($data);
    }

    public function updateAccountRegist(Request $request, Watemplate $id)
    {
        $account_regist = preg_replace("/\r\n|\r|\n/", '<br>', $request->account_regist);
        $tes = $id->update([
            'account_regist' => $account_regist,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }

    public function updateAccountActive(Request $request, Watemplate $id)
    {
        $account_active = preg_replace("/\r\n|\r|\n/", '<br>', $request->account_active);
        $id->update([
            'account_active' => $account_active,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }

    public function updateAccountSuspend(Request $request, Watemplate $id)
    {
        $account_suspend = preg_replace("/\r\n|\r|\n/", '<br>', $request->account_suspend);
        $id->update([
            'account_suspend' => $account_suspend,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }

    public function updateInvoiceTerbit(Request $request, Watemplate $id)
    {
        $invoice_terbit = preg_replace("/\r\n|\r|\n/", '<br>', $request->invoice_terbit);
        $id->update([
            'invoice_terbit' => $invoice_terbit,
            // 'invoice_overdue' => $request->invoice_overdue,
            // 'payment_paid' => $request->payment_paid,
            // 'payment_cancel' => $request->payment_cancel,
            // 'account_active' => $request->account_active,
            // 'account_suspend' => $request->account_suspend,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }
    public function updateInvoiceReminder(Request $request, Watemplate $id)
    {
        $invoice_reminder = preg_replace("/\r\n|\r|\n/", '<br>', $request->invoice_reminder);
        $id->update([
            'invoice_reminder' => $invoice_reminder,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }
    public function updateInvoiceOverdue(Request $request, Watemplate $id)
    {
        $invoice_overdue = preg_replace("/\r\n|\r|\n/", '<br>', $request->invoice_overdue);
        $id->update([
            'invoice_overdue' => $invoice_overdue,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }
    public function updatePaymentPaid(Request $request, Watemplate $id)
    {
        $payment_paid = preg_replace("/\r\n|\r|\n/", '<br>', $request->payment_paid);
        $id->update([
            'payment_paid' => $payment_paid,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }
    public function updatePaymentCancel(Request $request, Watemplate $id)
    {
        $payment_cancel = preg_replace("/\r\n|\r|\n/", '<br>', $request->payment_cancel);
        $id->update([
            'payment_cancel' => $payment_cancel,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }

    public function updateOpenPelanggan(Request $request, Watemplate $id)
    {
        $tiket_open_pelanggan = preg_replace("/\r\n|\r|\n/", '<br>', $request->tiket_open_pelanggan);
        $id->update([
            'tiket_open_pelanggan' => $tiket_open_pelanggan,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }

    public function updateOpenTeknisi(Request $request, Watemplate $id)
    {
        $tiket_open_teknisi = preg_replace("/\r\n|\r|\n/", '<br>', $request->tiket_open_teknisi);
        $id->update([
            'tiket_open_teknisi' => $tiket_open_teknisi,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }

    public function updateClosedPelanggan(Request $request, Watemplate $id)
    {
        $tiket_close_pelanggan = preg_replace("/\r\n|\r|\n/", '<br>', $request->tiket_close_pelanggan);
        $id->update([
            'tiket_close_pelanggan' => $tiket_close_pelanggan,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }

    public function updateClosedTeknisi(Request $request, Watemplate $id)
    {
        $tiket_close_teknisi = preg_replace("/\r\n|\r|\n/", '<br>', $request->tiket_close_teknisi);
        $id->update([
            'tiket_close_teknisi' => $tiket_close_teknisi,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $id,
        ]);
    }

    public function resendMessage(Request $request)
    {
        set_time_limit(0);
        $whatsapp = Mpwa::where('shortname', multi_auth()->shortname)->first();
        $multiCurl = [];
        $result = [];
        $mh = curl_multi_init();

        foreach ($request->ids as $id) {
            $data = [
                'api_key' => $whatsapp->api_key,
                'id' => $id,
            ];

            $curl = curl_init();
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_URL, 'https://' . $whatsapp->mpwa_server . '/resend-message/api');
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);

            curl_multi_add_handle($mh, $curl);
            $multiCurl[] = $curl;
        }

        // Eksekusi semua request secara paralel
        $running = null;
        do {
            curl_multi_exec($mh, $running);
            curl_multi_select($mh);
        } while ($running > 0);

        // Ambil hasil dan tutup masing-masing cURL
        foreach ($multiCurl as $curl) {
            $response = curl_multi_getcontent($curl);
            $result[] = $response;
            curl_multi_remove_handle($mh, $curl);
            curl_close($curl);
        }
        curl_multi_close($mh);

        return response()->json([
            'success' => true,
            'message' => 'Pesan berhasil dikirim ulang',
            'data' => $result,
        ]);
    }
    public function deleteMessage(Request $request)
    {
        set_time_limit(0);
        $whatsapp = Mpwa::where('shortname', multi_auth()->shortname)->first();
        $multiHandle = curl_multi_init();
        $curlHandles = [];
        $responses = [];

        try {
            // Inisialisasi setiap cURL handle untuk setiap id
            foreach ($request->ids as $id) {
                $curl = curl_init();
                $data = [
                    'api_key' => $whatsapp->api_key,
                    'id' => $id,
                ];

                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
                curl_setopt($curl, CURLOPT_URL, 'https://' . $whatsapp->mpwa_server . '/delete-message/api');
                curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
                curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);

                // Simpan handle untuk kemudian diproses dan mapping ke id
                $curlHandles[$id] = $curl;
                curl_multi_add_handle($multiHandle, $curl);
            }

            // Eksekusi semua cURL secara paralel
            $running = null;
            do {
                curl_multi_exec($multiHandle, $running);
                curl_multi_select($multiHandle);
            } while ($running > 0);

            // Ambil respon dari masing-masing handle
            foreach ($curlHandles as $id => $curl) {
                $response = curl_multi_getcontent($curl);
                $responses[$id] = json_decode($response, true);
                // Hapus handle individual
                curl_multi_remove_handle($multiHandle, $curl);
                curl_close($curl);
            }

            curl_multi_close($multiHandle);

            return response()->json([
                'success' => true,
                'message' => 'Pesan berhasil dihapus',
                'responses' => $responses, // Optional: untuk menampilkan respon tiap request
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    // public function getUser(Request $request)
    // {
    //     $countuserbyArea = Member::where('group_id', multi_auth()->shortname)
    //         ->where('kode_area', $request->kode_area)
    //         ->count();
    //     $userbyArea = Member::where('group_id', multi_auth()->shortname)
    //         ->where('kode_area', $request->kode_area)
    //         ->select('kode_area', 'wa')
    //         ->get();
    //     return response()->json([
    //         'success' => true,
    //         'count' => $countuserbyArea,
    //         'data' => $userbyArea,
    //     ]);
    // }

    public function getAllUserActive()
    {
        $countuser = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 1)->count();
        $user = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 1)->select('wa')->get();
        return response()->json([
            'success' => true,
            'countuser' => $countuser,
            'data' => $user,
        ]);
    }

    public function getAllUserActive_owner()
    {
        $countuser = User::where('role', 'Admin')->where('status', 1)->count();
        $user = User::where('role', 'Admin')->where('status', 1)->select('whatsapp')->get();
        return response()->json([
            'success' => true,
            'countuser' => $countuser,
            'data' => $user,
        ]);
    }

    public function getAllUserTrial_owner()
    {
        $countuser = User::where('role', 'Admin')->where('license_id', 1)->count();
        $user = User::where('role', 'Admin')->where('license_id', 1)->select('whatsapp')->get();
        return response()->json([
            'success' => true,
            'countuser' => $countuser,
            'data' => $user,
        ]);
    }

    public function getAllUserExpired_owner()
    {
        $countuser = User::where('role', 'Admin')->where('status', 3)->whereNot('license_id', 1)->count();
        $user = User::where('role', 'Admin')->where('status', 3)->whereNot('license_id', 1)->select('whatsapp')->get();
        return response()->json([
            'success' => true,
            'countuser' => $countuser,
            'data' => $user,
        ]);
    }

    public function getAllUserSuspend()
    {
        $countuser = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 2)->count();
        $user = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 2)->select('wa')->get();
        return response()->json([
            'success' => true,
            'countuser' => $countuser,
            'data' => $user,
        ]);
    }

    public function getAllUserArea(Request $request)
    {
        $countuser = PppoeUser::where('shortname', multi_auth()->shortname)->where('kode_area', $request->kode_area)->count();
        $user = PppoeUser::where('shortname', multi_auth()->shortname)->where('kode_area', $request->kode_area)->select('wa')->get();
        return response()->json([
            'success' => true,
            'countuser' => $countuser,
            'data' => $user,
        ]);
    }

    public function getAllUserOdp(Request $request)
    {
        $countuser = PppoeUser::where('shortname', multi_auth()->shortname)->where('kode_odp', $request->kode_odp)->count();
        $user = PppoeUser::where('shortname', multi_auth()->shortname)->where('kode_odp', $request->kode_odp)->select('wa')->get();
        return response()->json([
            'success' => true,
            'countuser' => $countuser,
            'data' => $user,
        ]);
    }

    public function sendBroadcast(Request $request)
    {
        // $user = Mpwa::where('shortname', multi_auth()->shortname)->first();
        // try {
        //     $curl = curl_init();
        //     $data = [
        //         'api_key' => $user->api_key,
        //         'sender' => $user->sender,
        //         'number' => implode('|', $request->wa),
        //         'message' => $request->message,
        //     ];
        //     curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
        //     curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        //     curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        //     curl_setopt($curl, CURLOPT_URL, 'https://' . $user->mpwa_server . '/send-message');
        //     curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        //     curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        //     $response = curl_exec($curl);
        //     curl_close($curl);
        //     $result = json_decode($response, true);
        //     sleep(5);
        //     if ($result['status'] = true) {
        //         return response()->json([
        //             'success' => true,
        //             'message' => 'Pesan broadcast berhasil terkirim',
        //         ]);
        //     }
        // } catch (\Exception $e) {
        //     return $e->getMessage();
        // }

        $user = Mpwa::where('shortname', multi_auth()->shortname)->first();

        // try {
        //     foreach ($request->wa as $number) {
        //         $curl = curl_init();
        //         $data = [
        //             'api_key' => $user->api_key,
        //             'sender' => $user->sender,
        //             'number' => $number,
        //             'message' => $request->message,
        //         ];
        //         curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
        //         curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        //         curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        //         curl_setopt($curl, CURLOPT_URL, 'https://' . $user->mpwa_server . '/send-message');
        //         curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        //         curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        //         $response = curl_exec($curl);
        //         curl_close($curl);

        //         $result = json_decode($response, true);

        //         // Jeda random antara 3–7 detik
        //         sleep(mt_rand(3, 7));
        //     }

        //     return response()->json([
        //         'success' => true,
        //         'message' => 'Pesan broadcast berhasil terkirim ke semua nomor',
        //     ]);
        // } catch (\Exception $e) {
        //     return response()->json([
        //         'success' => false,
        //         'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
        //     ]);
        // }

        try {
            $multiHandle = curl_multi_init();
            $curlHandles = [];
        
            foreach ($request->wa as $number) {
                $curl = curl_init();
                $data = [
                    'api_key' => $user->api_key,
                    'sender' => $user->sender,
                    'number' => $number,
                    'message' => $request->message,
                ];
        
                curl_setopt_array($curl, [
                    CURLOPT_URL => 'https://' . $user->mpwa_server . '/send-message',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_POST => true,
                    CURLOPT_POSTFIELDS => http_build_query($data),
                    CURLOPT_SSL_VERIFYHOST => 0,
                    CURLOPT_SSL_VERIFYPEER => 0,
                ]);
        
                curl_multi_add_handle($multiHandle, $curl);
                $curlHandles[] = $curl;
            }
        
            // Eksekusi paralel
            $running = null;
            do {
                curl_multi_exec($multiHandle, $running);
                curl_multi_select($multiHandle);
            } while ($running > 0);
        
            // Ambil hasil dan tutup semua handle
            foreach ($curlHandles as $curl) {
                $response = curl_multi_getcontent($curl);
                $result = json_decode($response, true); // jika perlu diproses
                curl_multi_remove_handle($multiHandle, $curl);
                curl_close($curl);
            }
        
            curl_multi_close($multiHandle);
        
            return response()->json([
                'success' => true,
                'message' => 'Pesan broadcast berhasil dikirim secara paralel ke semua nomor',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ]);
        }
        
    }
}

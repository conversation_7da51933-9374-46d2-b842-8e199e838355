<?php

namespace App\Http\Controllers\Olt;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Library\ZteAPI;
use Exception;

class ZteController extends Controller
{
    protected ZteAPI $zte;

    public function __construct(Request $request)
    {
        $host = $request->get('host', '127.0.0.1');
        $community = $request->get('community', 'public');
        $port = (int) $request->get('port', 2161);
        $this->zte = new ZteAPI($host, $community, $port);
    }

    public function sysInfo()
    {
        try {
            return response()->json([
                'success'   => true,
                'sysName'   => $this->zte->getSysName(),
                'sysUptime' => $this->zte->getSysUptime(),
            ]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function cpuMemory()
    {
        try {
            return response()->json([
                'success'     => true,
                'cpuUsage'    => $this->zte->getCpuUsage(),
                'memoryUsage' => $this->zte->getMemoryUsage(),
            ]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function onuStatus()
    {
        try {
            return response()->json([
                'success' => true,
                'data'    => $this->zte->getOnuStatusList(),
            ]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function onuList()
    {
        try {
            return response()->json([
                'success' => true,
                'data'    => $this->zte->getOnuList(),
            ]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}

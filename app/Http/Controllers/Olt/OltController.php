<?php

namespace App\Http\Controllers\Olt;

use App\Http\Controllers\Controller;
use App\Models\Olt\OltDevice;
use Illuminate\Http\Request;
use App\Library\HsgqAPI;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class OltController extends Controller
{
    private $hsgqApi;

    public function __construct()
    {
        $this->hsgqApi = new HsgqAPI();
    }

    public function index(Request $request)
    {
        session()->forget('olt_session');
        $auth = multi_auth();
        $shortname = $auth->shortname;
        // $olt = OltDevice::where('shortname', $shortname)->orderByDesc('id')->get();

        if ($request->ajax()) {
            $oltQuery = OltDevice::query()->where('shortname', $shortname)->orderByDesc('id');

            return DataTables::of($oltQuery)
                ->addIndexColumn()
                ->addColumn('action', function ($row) use ($auth) {
                    return $this->getActionButtons($row, $auth->role);
                })
                ->toJson();
        }

        // Jika license_id == 2, tampilkan view limit account
        return $auth->license_id == 2 ? view('backend.account.limit') : view('backend.olt.index');
    }

    public function show($id)
    {
        $olt = OltDevice::findOrFail($id);
        return response()->json([
            'success' => true,
            'message' => 'Detail Data',
            'data' => $olt,
        ]);
    }

    public function store(Request $request)
    {
        $auth = multi_auth();

        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'min:3', 'max:255', Rule::unique('olt_device')->where('shortname', $auth->shortname)],
            'username' => 'required|string',
            'password' => 'required|string',
            'host' => ['required', 'string', Rule::unique('olt_device')],
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors(),
            ], 422);
        }

        $olt = OltDevice::create([
            'shortname' => $auth->shortname,
            'name' => $request->name,
            'type' => $request->type, // Pastikan input type sudah valid
            'host' => $request->host,
            'username' => $request->username,
            'password' => $request->password,
            'cookies' => '812u37y123y721y3', // Nilai default; pertimbangkan untuk membuat mekanisme generate cookie
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $olt,
        ]);
    }

    public function update(Request $request, OltDevice $olt)
    {

        $olt->update([
            'name' => $request->name,
            'host' => $request->host,
            'type' => $request->type,
            'username' => $request->username,
            'password' => $request->password,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $olt,
        ]);
    }

    public function destroy($id)
    {
        $olt = OltDevice::findOrFail($id);
        $olt->delete();

        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Dihapus',
        ]);
    }

    /**
     * Menghasilkan HTML tombol action untuk DataTables.
     *
     * @param  \App\Models\Olt\OltDevice  $row
     * @param  string  $role
     * @return string
     */
    private function getActionButtons($row, $role)
    {
        // Tombol login berdasarkan tipe perangkat
        $loginButton =
            $row->type === 'HIOSO 2 PON' || $row->type === 'HIOSO 4 PON'
                ? '<a href="javascript:void(0)" id="login-hioso" data-id="' .
                    $row->id .
                    '" class="">
                    <i class="ki-filled ki-entrance-left text-lg"></i>
               </a>'
                : '<a href="javascript:void(0)" id="login-hsgq" data-id="' .
                    $row->id .
                    '" class="">
                    <i class="ki-filled ki-entrance-left text-lg"></i>
               </a>';

        // Jika role Admin, tambahkan tombol edit dan delete
        $adminButtons =
            $role === 'Admin'
                ? '<a href="javascript:void(0)" id="edit" data-id="' .
                    $row->id .
                    '" class="">
                    <i class="ki-filled ki-notepad-edit text-lg"></i>
               </a>
               <a href="javascript:void(0)" id="delete" data-id="' .
                    $row->id .
                    '" class="">
                    <i class="ki-filled ki-trash text-lg"></i>
               </a>'
                : '';

        return $loginButton . $adminButtons;
    }
}

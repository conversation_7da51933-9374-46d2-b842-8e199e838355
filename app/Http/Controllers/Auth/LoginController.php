<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cookie;
use App\Models\Partnership\Mitra;
use App\Models\Partnership\Reseller;
use Session;
use Illuminate\Support\Facades\Response;

class LoginController extends Controller
{
    // public function login()
    // {
    //     if (Auth::guard('web')->check() || Auth::guard('mitra')->check()) {
    //         return redirect('/dashboard');
    //     } else {
    //         return view('backend/auth/login');
    //     }
    // }

    // public function auth(Request $request)
    // {
    //     $data = [
    //         'username' => $request->input('username'),
    //         'password' => $request->input('password'),
    //     ];

    //     // Coba login menggunakan mekanisme default (misalnya tabel users)
    //     if (Auth::attempt($data)) {
    //         return redirect('/');
    //     }
    //     // Ambil user dari table lain
    //     $mitra = Mitra::where('id_mitra', $data['username'])->first();

    //     // Cek apakah user ditemukan dan password sesuai
    //     if ($mitra && Hash::check($data['password'], $mitra->password)) {
    //         Auth::guard('mitra')->login($mitra);
    //         return redirect('/');
    //     }

    //     Session::flash('error', 'Username atau password salah');
    //     return redirect('/');
    // }

    // public function logout()
    // {
    //     if (Auth::guard('web')->check()) {
    //         Auth::guard('web')->logout();
    //     }
    //     if (Auth::guard('mitra')->check()) {
    //         Auth::guard('mitra')->logout();
    //     }
    //     return redirect('/');
    // }

    public function login(Request $request)
    {
        // Jika user sudah login di salah satu guard, redirect ke dashboard
        if (Auth::guard('web')->check() || Auth::guard('mitra')->check() || Auth::guard('reseller')->check()) {
            return redirect()->route('dashboard');
        }
        return view('backend.auth.login');
    }

    // public function auth(Request $request)
    // {
    //     // Validasi input untuk keamanan dan konsistensi data
    //     $credentials = $request->validate([
    //         'username' => 'required|string',
    //         'password' => 'required|string',
    //     ]);

    //     // Coba login menggunakan mekanisme default (misalnya tabel users)
    //     if (Auth::attempt($credentials)) {
    //         // Regenerasi session untuk mencegah session fixation
    //         $request->session()->regenerate();
    //         return redirect()->intended('/');
    //     }

    //     // Coba login sebagai mitra (dari tabel lain)
    //     $mitra = Mitra::where('id_mitra', $credentials['username'])->first();
    //     if ($mitra && Hash::check($credentials['password'], $mitra->password)) {
    //         Auth::guard('mitra')->login($mitra);
    //         $request->session()->regenerate();
    //         return redirect()->intended('/');
    //     }

    //     // Jika autentikasi gagal, kembalikan ke halaman login dengan pesan error
    //     Session::flash('error', 'Username atau password salah');
    //     return redirect('/');
    // }

    // public function logout(Request $request)
    // {
    //     // Logout dari kedua guard tanpa perlu cek terlebih dahulu,
    //     // karena logout pada guard yang tidak aktif tidak menimbulkan error.
    //     Auth::guard('web')->logout();
    //     Auth::guard('mitra')->logout();

    //     // Invalidate session dan regenerasi token untuk keamanan
    //     $request->session()->invalidate();
    //     $request->session()->regenerate();

    //     return redirect('/');
    // }

    public function auth(Request $request)
    {
        $credentials = $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);
    
        if (Auth::attempt($credentials, false)) {
            $request->session()->regenerate(); // 💥 reset session ID + token
    
            // \Log::info('🔍 Login web', [
            //     'session_id' => session()->getId(),
            //     'user_id_web' => auth()->id(),
            // ]);
    
            return redirect()->intended('/');
        }
    
        $mitra = Mitra::where('id_mitra', $credentials['username'])->first();
        if ($mitra && Hash::check($credentials['password'], $mitra->password)) {
            Auth::guard('mitra')->login($mitra);
            $request->session()->regenerate();
    
            return redirect()->intended('/');
        }
    
        $reseller = Reseller::where('id_reseller', $credentials['username'])->first();
        if ($reseller && Hash::check($credentials['password'], $reseller->password)) {
            Auth::guard('reseller')->login($reseller);
            $request->session()->regenerate();
    
            return redirect()->intended('/');
        }
    
        return back()->withErrors(['login' => 'Username atau password salah'])
                     ->withInput($request->only('username'));
    }    

    public function logout(Request $request)
{
    foreach (['web', 'mitra', 'reseller'] as $guard) {
        Auth::guard($guard)->logout();
    }

    $request->session()->invalidate();     // invalidate session ID lama
    $request->session()->flush();          // flush semua data
    $request->session()->regenerate(); // buat CSRF token baru

    // logger('🔒 Logout', [
    //     'session_id' => session()->getId(),
    // ]);

    return redirect('/auth')->with('success', 'Logout berhasil');
}

}

<?php

namespace App\Http\Controllers\Mapping;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Mapping\Pop;
use App\Models\Mapping\Odp;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Models\Pppoe\PppoeUser;

class AreaController extends Controller
{
    public function index()
    {
        if (request()->ajax()) {
            $areas = Pop::query()
                ->where('shortname', multi_auth()->shortname)
                ->orderBy('id', 'desc');
            return DataTables::of($areas)
                ->addIndexColumn()
                ->addColumn('jml_odp', function ($row) {
                    return $row
                        ->hasMany(Odp::class, 'kode_area_id', 'id')
                        ->where('shortname', multi_auth()->shortname)
                        ->count();
                })
                ->addColumn('jml_plgn', function ($row) {
                    return $row
                        ->hasMany(PppoeUser::class, 'kode_area', 'kode_area')
                        ->where('shortname', multi_auth()->shortname)
                        ->count();
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="kt-menu flex-inline" data-kt-menu="true">
                        <div class="kt-menu-item kt-menu-item-dropdown" data-kt-menu-item-offset="0, 10px" data-kt-menu-item-placement="bottom-end" data-kt-menu-item-placement-rtl="bottom-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click">
                            <button class="kt-menu-toggle kt-btn kt-btn-sm kt-btn-icon kt-btn-ghost">
                                <i class="ki-filled ki-dots-vertical text-lg"></i>
                            </button>
                            <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px]" data-kt-menu-dismiss="true" style="z-index: 105; position: fixed; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-45px, 343px, 0px);" data-popper-placement="bottom-end">
                                <div class="kt-menu-item">
                                    <a class="kt-menu-link" href="javascript:void(0)" id="edit" data-id="' . $row->id . '">
                                        <span class="kt-menu-icon">
                                            <i class="ki-filled ki-notepad-edit"></i>
                                        </span>
                                        <span class="kt-menu-title">Edit</span>
                                    </a>
                                </div>
                                <div class="kt-menu-item">
                                    <a class="kt-menu-link text-red-500" href="javascript:void(0)" id="delete" data-id="' . $row->id . '">
                                        <span class="kt-menu-icon">
                                            <i class="ki-filled ki-trash-square"></i>
                                        </span>
                                        <span class="kt-menu-title">Hapus</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>';
                })
                ->toJson();
        }
        return view('backend.mapping.area.index');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'kode_area' => [
                'required',
                'string',
                'min:2',
                'max:255',
                Rule::unique('mapping_area')->where('shortname', multi_auth()->shortname),
            ],
            'deskripsi' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->errors(),
            ]);
        }

        $area = Pop::create([
            'shortname' => multi_auth()->shortname,
            'kode_area' => $request->kode_area,
            'deskripsi' => $request->deskripsi,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $area,
        ]);
    }

    public function show(Pop $pop)
    {
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Detail Data',
            'data' => $pop,
        ]);
    }

    public function update(Request $request, Pop $pop)
    {
        $validator = Validator::make($request->all(), [
            // 'kode_area' => ['required', 'string','min:2','max:255', 'unique:'.Pop::class],
            'kode_area' => 'required',
            'deskripsi' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->errors(),
            ]);
        }

        $pop->update([
            // 'kode_area' => $request->kode_area,
            'deskripsi' => $request->deskripsi,
        ]);

        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Diupdate',
            'data' => $pop,
        ]);
    }

    public function destroy($id)
    {
        $area = Pop::findOrFail($id);
        $area->delete();
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Dihapus',
        ]);
    }
}

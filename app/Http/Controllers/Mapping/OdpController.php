<?php

namespace App\Http\Controllers\Mapping;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Mapping\Pop;
use App\Models\Mapping\Odp;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Models\Pppoe\PppoeUser;

class OdpController extends Controller
{
    public function index()
    {
        $areas = Pop::where('shortname',multi_auth()->shortname)->orderBy('kode_area','asc')->get();
        if (request()->ajax()) {
            $odps = Odp::query()->where('shortname',multi_auth()->shortname)->with('area')->orderBy('id', 'desc');
            return DataTables::of($odps)
                ->addIndexColumn()
                ->editColumn('area.kode_area', function ($odps) {
                    return $odps->area->kode_area;
                })
                ->addColumn('jml_plgn', function ($row) {
                    return $row->hasMany(PppoeUser::class,'kode_odp','kode_odp')->where('shortname',multi_auth()->shortname)->count();
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="kt-menu flex-inline" data-kt-menu="true">
                        <div class="kt-menu-item kt-menu-item-dropdown" data-kt-menu-item-offset="0, 10px" data-kt-menu-item-placement="bottom-end" data-kt-menu-item-placement-rtl="bottom-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click">
                            <button class="kt-menu-toggle kt-btn kt-btn-sm kt-btn-icon kt-btn-ghost">
                                <i class="ki-filled ki-dots-vertical text-lg"></i>
                            </button>
                            <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px]" data-kt-menu-dismiss="true" style="z-index: 105; position: fixed; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-45px, 343px, 0px);" data-popper-placement="bottom-end">
                                <div class="kt-menu-item">
                                    <a class="kt-menu-link" href="javascript:void(0)" id="edit" data-id="' . $row->id . '">
                                        <span class="kt-menu-icon">
                                            <i class="ki-filled ki-notepad-edit"></i>
                                        </span>
                                        <span class="kt-menu-title">Edit</span>
                                    </a>
                                </div>
                                <div class="kt-menu-item">
                                    <a class="kt-menu-link text-red-500" href="javascript:void(0)" id="delete" data-id="' . $row->id . '">
                                        <span class="kt-menu-icon">
                                            <i class="ki-filled ki-trash-square"></i>
                                        </span>
                                        <span class="kt-menu-title">Hapus</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>';
                })
                ->toJson();
        }
        return view('backend.mapping.odp.index', compact('areas'));
    }
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'kode_odp' => [
                'required',
                'string',
                'min:2',
                'max:255',
                Rule::unique('mapping_odp')->where('shortname', multi_auth()->shortname),
            ],
            'port_odp' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->errors(),
            ]);
        }

        $odp = Odp::create([
            'shortname' => multi_auth()->shortname,
            'kode_odp' => $request->kode_odp,
            'deskripsi' => $request->deskripsi,
            'port_odp' => $request->port_odp,
            'kode_area_id' => $request->kode_area_id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
        ]);

        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $odp,
        ]);
    }

    public function show(Odp $odp)
    {
        //return response
        $data = Odp::where('kode_area_id', $odp->kode_area_id)
            ->with('area')
            ->find($odp->id);
        return response()->json([
            'success' => true,
            'message' => 'Detail Data',
            'data' => $data,
        ]);
    }
    public function update(Request $request, Odp $odp)
    {
        // $validator = Validator::make($request->all(), [
        //     'port_odp' => 'required|integer|min:1',
        // ]);

        // if ($validator->fails()) {
        //     return response()->json([
        //         'error' => $validator->errors(),
        //     ]);
        // }

        $odp->update([
            'kode_odp' => $request->kode_odp,
            'port_odp' => $request->port_odp,
            'deskripsi' => $request->deskripsi,
            'kode_area_id' => $request->kode_area_id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
        ]);

        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Diupdate',
            'data' => $odp,
        ]);
    }
    public function destroy($id)
    {
        $odp = Odp::findOrFail($id);
        $odp->delete();
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Dihapus',
        ]);
    }
}

<?php

namespace App\Http\Controllers\Partnership;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Partnership\Reseller;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use App\Models\Hotspot\HotspotUser;
use App\Models\Keuangan\Transaksi;

use App\Models\Hotspot\HotspotProfile;

class ResellerController extends Controller
{
    public function index()
    {
        $profiles = HotspotProfile::where('shortname', multi_auth()->shortname)->where('status', 1)->get();
        $resellers = Reseller::where('shortname', multi_auth()->shortname)->get();

        if (request()->ajax()) {
            $resellers = Reseller::query()->where('shortname', multi_auth()->shortname)->orderBy('id', 'desc');
            return DataTables::of($resellers)
                ->addIndexColumn()
                ->addColumn('jml_plgn', function ($row) {
                    return $row->hasMany(HotspotUser::class, 'reseller_id', 'id')->where('shortname', multi_auth()->shortname)->where('status', 1)->count();
                })
                ->addColumn('action', function ($row) {
                    if ($row->status === 1) {
                        return '
                        <div class="kt-menu flex-inline" data-kt-menu="true">
                            <div class="kt-menu-item kt-menu-item-dropdown" data-kt-menu-item-offset="0, 10px" data-kt-menu-item-placement="bottom-end" data-kt-menu-item-placement-rtl="bottom-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click">
                                <button class="kt-menu-toggle kt-btn kt-btn-sm kt-btn-icon kt-btn-ghost">
                                    <i class="ki-filled ki-dots-vertical text-lg"></i>
                                </button>
                                <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px]" data-kt-menu-dismiss="true" style="z-index: 105; position: fixed; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-45px, 343px, 0px);" data-popper-placement="bottom-end">
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link" href="javascript:void(0)" id="edit" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-notepad-edit"></i>
                                            </span>
                                            <span class="kt-menu-title">Edit</span>
                                        </a>
                                    </div>
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link text-yellow-500" href="javascript:void(0)" id="disable" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-cross-square"></i>
                                            </span>
                                            <span class="kt-menu-title">Nonaktifkan</span>
                                        </a>
                                    </div>
                                    <div class="kt-menu-separator"></div>
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link text-red-500" href="javascript:void(0)" id="delete" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-trash-square"></i>
                                            </span>
                                            <span class="kt-menu-title">Hapus</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>';
                    } else {
                        return '
                        <div class="kt-menu flex-inline" data-kt-menu="true">
                            <div class="kt-menu-item kt-menu-item-dropdown" data-kt-menu-item-offset="0, 10px" data-kt-menu-item-placement="bottom-end" data-kt-menu-item-placement-rtl="bottom-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click">
                                <button class="kt-menu-toggle kt-btn kt-btn-sm kt-btn-icon kt-btn-ghost">
                                    <i class="ki-filled ki-dots-vertical text-lg"></i>
                                </button>
                                <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px]" data-kt-menu-dismiss="true" style="z-index: 105; position: fixed; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-45px, 343px, 0px);" data-popper-placement="bottom-end">
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link" href="javascript:void(0)" id="edit" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-notepad-edit"></i>
                                            </span>
                                            <span class="kt-menu-title">Edit</span>
                                        </a>
                                    </div>
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link text-green-500" href="javascript:void(0)" id="enable" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-check-squared"></i>
                                            </span>
                                            <span class="kt-menu-title">Aktifkan</span>
                                        </a>
                                    </div>
                                    <div class="kt-menu-separator"></div>
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link text-red-500" href="javascript:void(0)" id="delete" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-trash-square"></i>
                                            </span>
                                            <span class="kt-menu-title">Hapus</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>';
                    }
                })
                ->toJson();
        }
        return view('backend.partnership.reseller.index', compact('profiles', 'resellers'));
    }

    public function getStats($id)
    {
        $reseller = Reseller::findOrFail($id);

        // Total semua voucher milik reseller
        $voucher_stok = HotspotUser::where('reseller_id', $id)->where('status', 1)->count();

        // Voucher aktif (status 1)
        $voucher_aktif = HotspotUser::where('reseller_id', $id)->where('status', 2)->count();

        // Voucher expired (status 0 atau misalnya status expired = 9, sesuaikan)
        $voucher_expired = HotspotUser::where('reseller_id', $id)->where('status', 3)->count();

        // Ambil nama reseller
        $resellerName = $reseller->name;

        // Filter dasar
        $query = Transaksi::where('shortname', multi_auth()->shortname)->where('kategori', 'Hotspot')->where('reseller', $resellerName);

        // Jumlah penjualan = total transaksi
        $total_penjualan = (clone $query)->count();

        $total_setoran = (clone $query)->where('tipe', 'Pemasukan')->sum('nominal');
        $total_keuntungan = (clone $query)->where('tipe', 'Pemasukan')->sum('fee_reseller');
        $total_pendapatan = $jumlah_pendapatan_bersih + $jumlah_fee_reseller;

        return response()->json([
            'name' => $reseller->name,
            'voucher_stok' => $voucher_stok,
            'voucher_aktif' => $voucher_aktif,
            'voucher_expired' => $voucher_expired,
            'total_penjualan' => $total_penjualan,
            'total_pendapatan' => $total_pendapatan,
            'total_setoran' => $total_setoran,
            'total_keuntungan' => $total_keuntungan,
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id_reseller' => ['required', 'string', 'min:5', 'max:10', Rule::unique('partnership_reseller')->where('shortname', multi_auth()->shortname)],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->errors(),
            ]);
        }
        $reseller = Reseller::create([
            'shortname' => multi_auth()->shortname,
            'name' => $request->nama_reseller,
            'id_reseller' => $request->id_reseller,
            'password' => Hash::make($request->password),
            'login' => 1,
            'nomor_wa' => $request->nomor_wa,
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $reseller,
        ]);
    }

    public function show(Reseller $reseller)
    {
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Detail Data',
            'data' => $reseller,
        ]);
    }

    public function update(Request $request, Reseller $reseller)
    {
        // $validator = Validator::make($request->all(), [
        //     'name' => 'required',
        //     'nomor_wa' => 'required',
        // ]);

        // if ($validator->fails()) {
        //     return response()->json([
        //         'error' => $validator->errors(),
        //     ]);
        // }

        if ($request->password == null) {
            $reseller->update([
                'name' => $request->nama_reseller,
                'id_reseller' => $request->id_reseller,
                'login' => 1,
                'nomor_wa' => $request->nomor_wa,
            ]);
        } elseif ($request->profile == null) {
            $reseller->update([
                'name' => $request->nama_reseller,
                'id_reseller' => $request->id_reseller,
                'login' => 1,
                'password' => Hash::make($request->password),
                'nomor_wa' => $request->nomor_wa,
            ]);
        } elseif ($request->password == null && $request->profile == null) {
            $reseller->update([
                'name' => $request->nama_reseller,
                'id_reseller' => $request->id_reseller,
                'login' => 1,
                'nomor_wa' => $request->nomor_wa,
            ]);
        } else {
            $reseller->update([
                'name' => $request->nama_reseller,
                'id_reseller' => $request->id_reseller,
                'password' => Hash::make($request->password),
                'login' => 1,
                'nomor_wa' => $request->nomor_wa,
            ]);
        }

        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Diupdate',
            'data' => $reseller,
        ]);
    }

    public function destroy($id)
    {
        $reseller = Reseller::findOrFail($id);
        $reseller->delete();
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Dihapus',
        ]);
    }

    public function disable(Request $request)
    {
        $reseller = Reseller::where('id', $request->id);
        $reseller->update([
            'status' => 0,
        ]);
        return response()->json([
            'success' => true,
            'message' => 'Reseller Berhasil Dinonaktifkan',
            'data' => $reseller,
        ]);
    }

    public function enable(Request $request)
    {
        $reseller = Reseller::where('id', $request->id);
        $reseller->update([
            'status' => 1,
        ]);
        return response()->json([
            'success' => true,
            'message' => 'Reseller Berhasil Diaktifkan',
            'data' => $reseller,
        ]);
    }
}

<?php

namespace App\Http\Controllers\Partnership;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Partnership\Mitra;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use App\Models\Pppoe\PppoeUser;

use App\Models\Pppoe\PppoeProfile;

class MitraController extends Controller
{
    public function index()
    {
        $profiles = PppoeProfile::where('shortname', multi_auth()->shortname)->get();
        if (request()->ajax()) {
            $mitras = Mitra::query()->where('shortname', multi_auth()->shortname)->orderBy('id', 'desc');
            return DataTables::of($mitras)
                ->addIndexColumn()
                ->addColumn('jml_plgn', function ($row) {
                    return $row->hasMany(PppoeUser::class, 'mitra_id', 'id')->where('shortname', multi_auth()->shortname)->count();
                })
                ->addColumn('action', function ($row) {
                    if ($row->status === 1) {
                        return '
                        <div class="kt-menu flex-inline" data-kt-menu="true">
                            <div class="kt-menu-item kt-menu-item-dropdown" data-kt-menu-item-offset="0, 10px" data-kt-menu-item-placement="bottom-end" data-kt-menu-item-placement-rtl="bottom-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click">
                                <button class="kt-menu-toggle kt-btn kt-btn-sm kt-btn-icon kt-btn-ghost">
                                    <i class="ki-filled ki-dots-vertical text-lg"></i>
                                </button>
                                <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px]" data-kt-menu-dismiss="true" style="z-index: 105; position: fixed; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-45px, 343px, 0px);" data-popper-placement="bottom-end">
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link" href="javascript:void(0)" id="edit" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-notepad-edit"></i>
                                            </span>
                                            <span class="kt-menu-title">Edit</span>
                                        </a>
                                    </div>
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link text-yellow-500" href="javascript:void(0)" id="disable" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-cross-square"></i>
                                            </span>
                                            <span class="kt-menu-title">Nonaktifkan</span>
                                        </a>
                                    </div>
                                    <div class="kt-menu-separator"></div>
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link text-red-500" href="javascript:void(0)" id="delete" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-trash-square"></i>
                                            </span>
                                            <span class="kt-menu-title">Hapus</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>';
                    } else {
                        return '
                        <div class="kt-menu flex-inline" data-kt-menu="true">
                            <div class="kt-menu-item kt-menu-item-dropdown" data-kt-menu-item-offset="0, 10px" data-kt-menu-item-placement="bottom-end" data-kt-menu-item-placement-rtl="bottom-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click">
                                <button class="kt-menu-toggle kt-btn kt-btn-sm kt-btn-icon kt-btn-ghost">
                                    <i class="ki-filled ki-dots-vertical text-lg"></i>
                                </button>
                                <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px]" data-kt-menu-dismiss="true" style="z-index: 105; position: fixed; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-45px, 343px, 0px);" data-popper-placement="bottom-end">
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link" href="javascript:void(0)" id="edit" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-notepad-edit"></i>
                                            </span>
                                            <span class="kt-menu-title">Edit</span>
                                        </a>
                                    </div>
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link text-green-500" href="javascript:void(0)" id="enable" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-check-squared"></i>
                                            </span>
                                            <span class="kt-menu-title">Aktifkan</span>
                                        </a>
                                    </div>
                                    <div class="kt-menu-separator"></div>
                                    <div class="kt-menu-item">
                                        <a class="kt-menu-link text-red-500" href="javascript:void(0)" id="delete" data-id="' .
                            $row->id .
                            '">
                                            <span class="kt-menu-icon">
                                                <i class="ki-filled ki-trash-square"></i>
                                            </span>
                                            <span class="kt-menu-title">Hapus</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>';
                    }
                })
                ->toJson();
        }
        return view('backend.partnership.mitra.index', compact('profiles'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id_mitra' => ['required', 'string', 'min:5', 'max:10', Rule::unique('partnership_mitra')->where('shortname', multi_auth()->shortname)],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->errors(),
            ]);
        }
        $mitra = Mitra::create([
            'shortname' => multi_auth()->shortname,
            'name' => $request->nama_mitra,
            'id_mitra' => $request->id_mitra,
            'password' => Hash::make($request->password),
            'login' => $request->login,
            'user' => $request->user,
            'billing' => $request->billing,
            'nomor_wa' => $request->nomor_wa,
            'profile' => json_encode($request->profile),
        ]);
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $mitra,
        ]);
    }

    public function show(Mitra $mitra)
    {
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Detail Data',
            'data' => $mitra,
        ]);
    }

    public function update(Request $request, Mitra $mitra)
    {
        // $validator = Validator::make($request->all(), [
        //     'name' => 'required',
        //     'nomor_wa' => 'required',
        // ]);

        // if ($validator->fails()) {
        //     return response()->json([
        //         'error' => $validator->errors(),
        //     ]);
        // }

        if ($request->password == null) {
            $mitra->update([
                'name' => $request->nama_mitra,
                'id_mitra' => $request->id_mitra,
                'login' => $request->login,
                'user' => $request->user,
                'billing' => $request->billing,
                'nomor_wa' => $request->nomor_wa,
                'profile' => json_encode($request->profile),
            ]);
        } elseif ($request->profile == null) {
            $mitra->update([
                'name' => $request->nama_mitra,
                'id_mitra' => $request->id_mitra,
                'login' => $request->login,
                'password' => Hash::make($request->password),
                'user' => $request->user,
                'billing' => $request->billing,
                'nomor_wa' => $request->nomor_wa,
            ]);
        } elseif ($request->password == null && $request->profile == null) {
            $mitra->update([
                'name' => $request->nama_mitra,
                'id_mitra' => $request->id_mitra,
                'login' => $request->login,
                'user' => $request->user,
                'billing' => $request->billing,
                'nomor_wa' => $request->nomor_wa,
            ]);
        } else {
            $mitra->update([
                'name' => $request->nama_mitra,
                'id_mitra' => $request->id_mitra,
                'password' => Hash::make($request->password),
                'login' => $request->login,
                'user' => $request->user,
                'billing' => $request->billing,
                'nomor_wa' => $request->nomor_wa,
                'profile' => json_encode($request->profile),
            ]);
        }

        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Diupdate',
            'data' => $mitra,
        ]);
    }

    public function destroy($id)
    {
        $mitra = Mitra::findOrFail($id);
        $mitra->delete();
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Dihapus',
        ]);
    }

    public function disable(Request $request)
    {
        $mitra = Mitra::where('id', $request->id);
        $mitra->update([
            'status' => 0,
        ]);
        return response()->json([
            'success' => true,
            'message' => 'Mitra Berhasil Dinonaktifkan',
            'data' => $mitra,
        ]);
    }

    public function enable(Request $request)
    {
        $mitra = Mitra::where('id', $request->id);
        $mitra->update([
            'status' => 1,
        ]);
        return response()->json([
            'success' => true,
            'message' => 'Mitra Berhasil Diaktifkan',
            'data' => $mitra,
        ]);
    }
}

<?php

namespace App\Http\Controllers\Keuangan;

use App\Http\Controllers\Controller;
use App\Models\Keuangan\KategoriKeuangan;
use Illuminate\Http\Request;
use App\Models\Keuangan\Transaksi;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;
use Spatie\Activitylog\Contracts\Activity;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Excel as ExcelFormat;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Exports\TransaksiExport;
use App\Models\Setting\Company;
use App\Models\Mikrotik\Nas;
use App\Models\User;
use App\Models\Partnership\Reseller;

class ReportController extends Controller
{
    public function reportDaily(Request $request)
    {
        $kategori_pemasukan = KategoriKeuangan::where('shortname', multi_auth()->shortname)->where('type', 'Pemasukan')->get();
        $kategori_pengeluaran = KategoriKeuangan::where('shortname', multi_auth()->shortname)->where('type', 'Pengeluaran')->get();
        $users = User::where('shortname', multi_auth()->shortname)->get();

        $income_this_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->whereYear('tanggal', Carbon::today()->year)
            ->whereMonth('tanggal', Carbon::today()->month)
            ->sum('nominal');
        $expense_this_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pengeluaran')
            ->whereYear('tanggal', Carbon::today()->year)
            ->whereMonth('tanggal', Carbon::today()->month)
            ->sum('nominal');

        $income_last_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->whereYear('tanggal', Carbon::today()->subMonth()->year)
            ->whereMonth('tanggal', Carbon::today()->subMonth()->month)
            ->sum('nominal');

        $expense_last_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pengeluaran')
            ->whereYear('tanggal', Carbon::today()->subMonth()->year)
            ->whereMonth('tanggal', Carbon::today()->subMonth()->month)
            ->sum('nominal');
        $profit_this_month = $income_this_month - $expense_this_month;
        $profit_last_month = $income_last_month - $expense_last_month;

        if (request()->ajax()) {
            $transaksi = Transaksi::query()->where('shortname', multi_auth()->shortname);
            if ($request->filled('tanggal')) {
                $tanggal = Carbon::parse($request->tanggal)->format('Y-m-d');
                $transaksi->whereDate('tanggal', $tanggal);
            }
            if ($request->filled('tipe')) {
                $transaksi->where('tipe', $request->tipe);
            }
            if ($request->filled('kategori')) {
                $transaksi->where('kategori', $request->kategori);
            }
            if ($request->filled('created_by')) {
                $transaksi->where('created_by', $request->created_by);
            }

            $baseQuery = clone $transaksi;

            // Agregasi manual
            $tunaiPemasukan = (clone $baseQuery)->where('metode', 'Cash')->where('tipe', 'Pemasukan')->sum('nominal');
            $tunaiPengeluaran = (clone $baseQuery)->where('metode', 'Cash')->where('tipe', 'Pengeluaran')->sum('nominal');

            $transferPemasukan = (clone $baseQuery)->where('metode', 'Transfer')->where('tipe', 'Pemasukan')->sum('nominal');
            $transferPengeluaran = (clone $baseQuery)->where('metode', 'Transfer')->where('tipe', 'Pengeluaran')->sum('nominal');

            $totalPemasukan = (clone $baseQuery)->where('tipe', 'Pemasukan')->sum('nominal');
            $totalPengeluaran = (clone $baseQuery)->where('tipe', 'Pengeluaran')->sum('nominal');

            $tanggalFooter = $request->filled('tanggal') ? Carbon::parse($request->tanggal)->translatedFormat('d F Y') : now()->translatedFormat('d F Y');

            return DataTables::of($transaksi)
                ->addIndexColumn()
                ->with([
                    'footer' => [
                        'tunai_pemasukan' => $this->formatRupiah($tunaiPemasukan),
                        'tunai_pengeluaran' => $this->formatRupiah($tunaiPengeluaran),
                        'tunai_total' => $this->formatRupiah($tunaiPemasukan - $tunaiPengeluaran),

                        'transfer_pemasukan' => $this->formatRupiah($transferPemasukan),
                        'transfer_pengeluaran' => $this->formatRupiah($transferPengeluaran),
                        'transfer_total' => $this->formatRupiah($transferPemasukan - $transferPengeluaran),

                        'total_pemasukan' => $this->formatRupiah($totalPemasukan),
                        'total_pengeluaran' => $this->formatRupiah($totalPengeluaran),
                        'total_bersih' => $this->formatRupiah($totalPemasukan - $totalPengeluaran),

                        'tanggal' => $tanggalFooter,
                    ],
                ])
                ->toJson();
        }
        return view('backend.keuangan.report.index', compact('kategori_pemasukan', 'kategori_pengeluaran', 'users', 'income_this_month', 'expense_this_month', 'income_last_month', 'expense_last_month', 'profit_this_month', 'profit_last_month'));
    }

    public function reportMonthly(Request $request)
    {
        $kategori_pemasukan = KategoriKeuangan::where('shortname', multi_auth()->shortname)->where('type', 'Pemasukan')->get();
        $kategori_pengeluaran = KategoriKeuangan::where('shortname', multi_auth()->shortname)->where('type', 'Pengeluaran')->get();
        $users = User::where('shortname', multi_auth()->shortname)->get();

        $income_this_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->whereYear('tanggal', Carbon::today()->year)
            ->whereMonth('tanggal', Carbon::today()->month)
            ->sum('nominal');
        $expense_this_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pengeluaran')
            ->whereYear('tanggal', Carbon::today()->year)
            ->whereMonth('tanggal', Carbon::today()->month)
            ->sum('nominal');

        $income_last_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->whereYear('tanggal', Carbon::today()->subMonth()->year)
            ->whereMonth('tanggal', Carbon::today()->subMonth()->month)
            ->sum('nominal');

        $expense_last_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pengeluaran')
            ->whereYear('tanggal', Carbon::today()->subMonth()->year)
            ->whereMonth('tanggal', Carbon::today()->subMonth()->month)
            ->sum('nominal');
        $profit_this_month = $income_this_month - $expense_this_month;
        $profit_last_month = $income_last_month - $expense_last_month;

        if (request()->ajax()) {
            $month = $request->filled('tanggal') ? Carbon::createFromFormat('Y-m', $request->tanggal)->month : now()->month;
            $year = $request->filled('tanggal') ? Carbon::createFromFormat('Y-m', $request->tanggal)->year : now()->year;

            // Buat list tanggal 1 s.d. akhir bulan
            $rangeTanggal = collect(range(1, Carbon::create($year, $month)->daysInMonth))->map(function ($day) use ($month, $year) {
                return Carbon::createFromDate($year, $month, $day)->format('Y-m-d');
            });

            // Ambil semua transaksi yang sesuai bulan
            $transaksi = Transaksi::query()->selectRaw('DATE(tanggal) as tanggal, metode, tipe, SUM(nominal) as total')->where('shortname', multi_auth()->shortname)->whereYear('tanggal', $year)->whereMonth('tanggal', $month)->groupByRaw('DATE(tanggal), metode, tipe');

            if ($request->filled('tipe')) {
                $transaksi->where('tipe', $request->tipe);
            }
            if ($request->filled('kategori')) {
                $transaksi->where('kategori', $request->kategori);
            }
            if ($request->filled('created_by')) {
                $transaksi->where('created_by', $request->created_by);
            }

            $transaksi = $transaksi->get();

            // Gabungkan data transaksi dengan tanggal
            $data = $rangeTanggal->map(function ($tanggal) use ($transaksi) {
                $row = [
                    'tanggal' => $tanggal,
                    'pemasukan_tunai' => 0,
                    'pemasukan_transfer' => 0,
                    'pengeluaran_tunai' => 0,
                    'pengeluaran_transfer' => 0,
                    'pendapatan_tunai' => 0,
                    'pendapatan_transfer' => 0,
                ];

                $items = $transaksi->where('tanggal', $tanggal);

                foreach ($items as $item) {
                    if ($item->tipe === 'Pemasukan' && $item->metode === 'Cash') {
                        $row['pemasukan_tunai'] = $item->total;
                    }

                    if ($item->tipe === 'Pemasukan' && $item->metode === 'Transfer') {
                        $row['pemasukan_transfer'] = $item->total;
                    }

                    if ($item->tipe === 'Pengeluaran' && $item->metode === 'Cash') {
                        $row['pengeluaran_tunai'] = $item->total;
                    }

                    if ($item->tipe === 'Pengeluaran' && $item->metode === 'Transfer') {
                        $row['pengeluaran_transfer'] = $item->total;
                    }
                }

                // Hitung pendapatan setelah loop
                $row['pendapatan_tunai'] = $row['pemasukan_tunai'] - $row['pengeluaran_tunai'];
                $row['pendapatan_transfer'] = $row['pemasukan_transfer'] - $row['pengeluaran_transfer'];

                return $row;
            });

            // Hitung total footer
            $footer = [
                'tunai_pemasukan' => $data->sum('pemasukan_tunai'),
                'tunai_pengeluaran' => $data->sum('pengeluaran_tunai'),
                'tunai_total' => $data->sum('pemasukan_tunai') - $data->sum('pengeluaran_tunai'),

                'transfer_pemasukan' => $data->sum('pemasukan_transfer'),
                'transfer_pengeluaran' => $data->sum('pengeluaran_transfer'),
                'transfer_total' => $data->sum('pemasukan_transfer') - $data->sum('pengeluaran_transfer'),

                'total_pemasukan' => $data->sum('pemasukan_tunai') + $data->sum('pemasukan_transfer'),
                'total_pengeluaran' => $data->sum('pengeluaran_tunai') + $data->sum('pengeluaran_transfer'),
                'total_bersih' => $data->sum('pemasukan_tunai') + $data->sum('pemasukan_transfer') - ($data->sum('pengeluaran_tunai') + $data->sum('pengeluaran_transfer')),
                'tanggal' => Carbon::createFromDate($year, $month)->translatedFormat('F Y'),
            ];

            return DataTables::of($data)
                ->addIndexColumn()
                ->with([
                    'footer' => [
                        'tunai_pemasukan' => $this->formatRupiah($footer['tunai_pemasukan']),
                        'tunai_pengeluaran' => $this->formatRupiah($footer['tunai_pengeluaran']),
                        'tunai_total' => $this->formatRupiah($footer['tunai_total']),

                        'transfer_pemasukan' => $this->formatRupiah($footer['transfer_pemasukan']),
                        'transfer_pengeluaran' => $this->formatRupiah($footer['transfer_pengeluaran']),
                        'transfer_total' => $this->formatRupiah($footer['transfer_total']),

                        'total_pemasukan' => $this->formatRupiah($footer['total_pemasukan']),
                        'total_pengeluaran' => $this->formatRupiah($footer['total_pengeluaran']),
                        'total_bersih' => $this->formatRupiah($footer['total_bersih']),

                        'tanggal' => $footer['tanggal'],
                    ],
                ])
                ->toJson();
        }

        return view('backend.keuangan.report.index_monthly', compact('kategori_pemasukan', 'kategori_pengeluaran', 'users', 'income_this_month', 'expense_this_month', 'income_last_month', 'expense_last_month', 'profit_this_month', 'profit_last_month'));
    }

    public function reportExport(Request $request)
    {
        $kategori_pemasukan = KategoriKeuangan::where('shortname', multi_auth()->shortname)->where('type', 'Pemasukan')->get();
        $kategori_pengeluaran = KategoriKeuangan::where('shortname', multi_auth()->shortname)->where('type', 'Pengeluaran')->get();
        $users = User::where('shortname', multi_auth()->shortname)->get();
        $resellers = Reseller::where('shortname', multi_auth()->shortname)->where('status', 1)->select('id', 'name', 'id_reseller')->get();
        $nas = Nas::where('shortname', multi_auth()->shortname)->select('ip_router', 'name')->get();

        $income_this_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->whereYear('tanggal', Carbon::today()->year)
            ->whereMonth('tanggal', Carbon::today()->month)
            ->sum('nominal');
        $expense_this_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pengeluaran')
            ->whereYear('tanggal', Carbon::today()->year)
            ->whereMonth('tanggal', Carbon::today()->month)
            ->sum('nominal');

        $income_last_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->whereYear('tanggal', Carbon::today()->subMonth()->year)
            ->whereMonth('tanggal', Carbon::today()->subMonth()->month)
            ->sum('nominal');

        $expense_last_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pengeluaran')
            ->whereYear('tanggal', Carbon::today()->subMonth()->year)
            ->whereMonth('tanggal', Carbon::today()->subMonth()->month)
            ->sum('nominal');
        $profit_this_month = $income_this_month - $expense_this_month;
        $profit_last_month = $income_last_month - $expense_last_month;

        if (request()->ajax()) {
            $now = \Carbon\Carbon::now();

            $transaksi = Transaksi::query()->where('shortname', multi_auth()->shortname);

            if ($request->filled('tanggal')) {
                try {
                    $tanggal = $request->tanggal;
                
                    if (is_array($tanggal)) {
                        [$start, $end] = $tanggal;
                    } elseif (str_contains($tanggal, ' - ')) {
                        [$start, $end] = explode(' - ', $tanggal);
                    } elseif (str_contains($tanggal, '-')) {
                        [$start, $end] = explode('-', $tanggal);
                    } else {
                        $start = $end = $tanggal;
                    }
                
                    $start = trim($start);
                    $end = trim($end);
                
                    if (empty($start) || empty($end)) {
                        throw new \Exception("Start atau end kosong");
                    }
                
                    $start = Carbon::createFromFormat('d/m/Y', $start)->startOfDay();
                    $end = Carbon::createFromFormat('d/m/Y', $end)->endOfDay();
                
                    // \Log::info('Tanggal filter:', [$start->toDateTimeString(), $end->toDateTimeString()]);
                
                    $transaksi->whereBetween('tanggal', [$start, $end]);
                } catch (\Exception $e) {
                    logger()->error('Gagal parsing tanggal: ' . $e->getMessage());
                }
                
            }else {
                // Default ke bulan & tahun sekarang
                $now = now();
                $transaksi->whereMonth('tanggal', $now->month)
                          ->whereYear('tanggal', $now->year);
            }

            if ($request->filled('reseller')) {
                $transaksi->where('reseller', $request->reseller);
            }
            // Filter Created By
            if ($request->filled('created_by')) {
                $transaksi->where('created_by', $request->created_by);
            }

            // Filter Tipe
            if ($request->filled('tipe')) {
                $transaksi->where('tipe', $request->tipe);
            }

            // Filter Kategori
            if ($request->filled('kategori')) {
                $transaksi->where('kategori', $request->kategori);
            }

            if ($request->filled('nas')) {
                $transaksi->where('nas', $request->nas);
            }

            $total_income = (clone $transaksi)->where('tipe', 'Pemasukan')->sum('nominal');
            $total_expense = (clone $transaksi)->where('tipe', 'Pengeluaran')->sum('nominal');
            $total_fee_mitra = (clone $transaksi)->sum('nominal'); // asumsi kolom fee_mitra
            $total_fee_reseller = (clone $transaksi)->sum('fee_reseller'); // asumsi kolom fee_reseller

            $filteredData = $transaksi->orderBy('tanggal')->get();
            // Hitung saldo kumulatif
            $saldo = 0;
            $transaksi = $filteredData->map(function ($item) use (&$saldo) {
                $nominal = $item->nominal;
                if ($item->tipe === 'Pemasukan') {
                    $saldo += (int) $nominal;
                } elseif ($item->tipe === 'Pengeluaran') {
                    $saldo -= (int) $nominal;
                }
                $item->saldo = $saldo;
                return $item;
            });

            return DataTables::of($transaksi)
                ->addIndexColumn()
                ->with([
                    'summary' => [
                        'total_income' => number_format($total_income, 0, ',', '.'),
                        'total_expense' => number_format($total_expense, 0, ',', '.'),
                        'total_fee_mitra' => number_format($total_fee_mitra, 0, ',', '.'),
                        'total_fee_reseller' => number_format($total_fee_reseller, 0, ',', '.'),
                    ],
                ])
                ->addColumn('nominal', function ($row) {
                    return (int) $row->nominal;
                })                
                ->addColumn('action', function ($row) {
                    return '
                    <a href="javascript:void(0)" id="edit"
                    data-id="' .
                        $row->id .
                        '" class="btn btn-secondary text-white" style="--bs-btn-padding-y: .25rem; --bs-btn-padding-x: .5rem; --bs-btn-font-size: .75rem;">
                        <span class="material-symbols-outlined">edit</span>
                </a>
                <a href="javascript:void(0)" id="delete" data-id="' .
                        $row->id .
                        '" class="btn btn-danger text-white" style="--bs-btn-padding-y: .25rem; --bs-btn-padding-x: .5rem; --bs-btn-font-size: .75rem;">
                <span class="material-symbols-outlined">delete</span>
                </a>';
                })
                ->toJson();
        }
        return view('backend.keuangan.report.export', compact('kategori_pemasukan', 'kategori_pengeluaran', 'users', 'resellers','nas','income_this_month', 'expense_this_month', 'income_last_month', 'expense_last_month', 'profit_this_month', 'profit_last_month'));
    }

    private function formatRupiah($angka)
    {
        return 'Rp ' . number_format($angka, 0, ',', '.');
    }

    public function export(Request $request)
    {
        if ($request->filled('periode')) {
            try {
                if (str_contains($request->periode, ' - ')) {
                    [$start, $end] = explode(' - ', $request->periode);
                } else {
                    $start = $end = $request->periode;
                }
        
                $start = Carbon::createFromFormat('d/m/Y', trim($start))->startOfDay();
                $end = Carbon::createFromFormat('d/m/Y', trim($end))->endOfDay();
            } catch (\Exception $e) {
                return back()->with('error', 'Format tanggal tidak valid');
            }
        }
        $periodeLabel = isset($start)
        ? $start->translatedFormat('d M Y') . ' - ' . $end->translatedFormat('d M Y')
        : 'Periode Tidak Valid';
        $company = Company::where('shortname', multi_auth()->shortname)->first();
        return Excel::download(new TransaksiExport($request), 'Laporan Keuangan ' . $company->name . ' - ' . $periodeLabel . '.xlsx');
    }

    public function store(Request $request)
    {
        // $validator = Validator::make($request->all(), [
        //     'id_data' => ['required', 'string', 'min:5', Rule::unique('keuangan_transaksi')->where('shortname', multi_auth()->shortname)],
        // ]);

        // if ($validator->fails()) {
        //     return response()->json([
        //         'error' => $validator->errors(),
        //     ]);
        // }

        if ($request->tipe === 'Pemasukan') {
            $kategori = $request->kategori_income;
        } else {
            $kategori = $request->kategori_expense;
        }
        $transaksi = Transaksi::create([
            'shortname' => multi_auth()->shortname,
            'id_data' => rand(00000, 99999),
            'tanggal' => $request->tanggal,
            'tipe' => $request->tipe,
            'kategori' => $kategori,
            'deskripsi' => $request->deskripsi,
            'nominal' => str_replace('.', '', $request->nominal),
            'metode' => $request->metode,
            'created_by' => multi_auth()->username,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $transaksi,
        ]);
    }

    public function update(Request $request, Transaksi $transaksi)
    {
        if ($request->tipe === 'Pemasukan') {
            $kategori = $request->kategori_income;
        } else {
            $kategori = $request->kategori_expense;
        }
        $transaksi->update([
            'kategori' => $kategori,
            'deskripsi' => $request->deskripsi,
            'nominal' => str_replace('.', '', $request->nominal),
            'metode' => $request->metode,
        ]);
        activity()
            ->tap(function (Activity $activity) {
                $activity->shortname = multi_auth()->shortname;
            })
            ->event('Update')
            ->log('Update Transaction: ' . $request->deskripsi . '');
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Diupdate',
            'data' => $transaksi,
        ]);
    }

    public function show(Transaksi $transaksi)
    {
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Detail Data',
            'data' => $transaksi,
        ]);
    }

    public function destroy($id)
    {
        $transaksi = Transaksi::findOrFail($id);
        $deskripsi = Transaksi::where('id', $id)->first();
        activity()
            ->tap(function (Activity $activity) {
                $activity->shortname = multi_auth()->shortname;
            })
            ->event('Delete')
            ->log('Delete Transaction: ' . $deskripsi->deskripsi . '');
        $transaksi->delete();
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Dihapus',
            'data' => $transaksi,
        ]);
    }
}

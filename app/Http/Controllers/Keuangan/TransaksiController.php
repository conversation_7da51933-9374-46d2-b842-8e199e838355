<?php

namespace App\Http\Controllers\Keuangan;

use App\Http\Controllers\Controller;
use App\Models\Keuangan\KategoriKeuangan;
use Illuminate\Http\Request;
use App\Models\Keuangan\Transaksi;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;
use Spatie\Activitylog\Contracts\Activity;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Excel as ExcelFormat;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Exports\TransaksiExport;
use App\Models\Setting\Company;
use App\Models\Mikrotik\Nas;
use App\Models\User;

class TransaksiController extends Controller
{
    public function index(Request $request)
    {
        $kategori_pemasukan = KategoriKeuangan::where('shortname', multi_auth()->shortname)->where('type', 'Pemasukan')->get();
        $kategori_pengeluaran = KategoriKeuangan::where('shortname', multi_auth()->shortname)->where('type', 'Pengeluaran')->get();
        $users = User::where('shortname', multi_auth()->shortname)->get();

        $income_this_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->whereYear('tanggal', Carbon::today()->year)
            ->whereMonth('tanggal', Carbon::today()->month)
            ->sum('nominal');
        $expense_this_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pengeluaran')
            ->whereYear('tanggal', Carbon::today()->year)
            ->whereMonth('tanggal', Carbon::today()->month)
            ->sum('nominal');

        $income_last_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->whereYear('tanggal', Carbon::today()->subMonth()->year)
            ->whereMonth('tanggal', Carbon::today()->subMonth()->month)
            ->sum('nominal');
        
        $expense_last_month = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pengeluaran')
            ->whereYear('tanggal', Carbon::today()->subMonth()->year)
            ->whereMonth('tanggal', Carbon::today()->subMonth()->month)
            ->sum('nominal');
            $profit_this_month = $income_this_month - $expense_this_month;
            $profit_last_month = $income_last_month - $expense_last_month;

            $total_income = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->sum('nominal');
            $total_expense = Transaksi::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pengeluaran')
            ->sum('nominal');

            $total_saldo = $total_income - $total_expense;

        
            if (request()->ajax()) {
                $transaksi = Transaksi::query()
                    ->where('shortname', multi_auth()->shortname);
            
                // Filter berdasarkan bulan (dari <input type="month">)
                if ($request->filled('tanggal')) {
                    try {
                        $tanggal = Carbon::createFromFormat('Y-m', $request->tanggal);
                        $start = $tanggal->startOfMonth()->format('Y-m-d 00:00:00');
                        $end = $tanggal->endOfMonth()->format('Y-m-d 23:59:59');
            
                        $transaksi->whereBetween('tanggal', [$start, $end]);
                    } catch (\Exception $e) {
                        // log error jika format tidak valid (opsional)
                    }
                }
            
                // Filter Created By
                if ($request->filled('created_by')) {
                    $transaksi->where('created_by', $request->created_by);
                }
            
                // Filter Tipe
                if ($request->filled('tipe')) {
                    $transaksi->where('tipe', $request->tipe);
                }
            
                // Filter Kategori
                if ($request->filled('kategori')) {
                    $transaksi->where('kategori', $request->kategori);
                }
            
            return DataTables::of($transaksi)
                ->addIndexColumn()
                ->addColumn('action', function ($row) {
                        return '
                    <a href="javascript:void(0)" id="edit"
                    data-id="' .
                            $row->id .
                            '" class="btn btn-secondary text-white" style="--bs-btn-padding-y: .25rem; --bs-btn-padding-x: .5rem; --bs-btn-font-size: .75rem;">
                        <span class="material-symbols-outlined">edit</span>
                </a>
                <a href="javascript:void(0)" id="delete" data-id="' .
                            $row->id .
                            '" class="btn btn-danger text-white" style="--bs-btn-padding-y: .25rem; --bs-btn-padding-x: .5rem; --bs-btn-font-size: .75rem;">
                <span class="material-symbols-outlined">delete</span>
                </a>';
                })
                ->toJson();
        }
        return view('backend.keuangan.transaksi.index', compact('kategori_pemasukan', 'kategori_pengeluaran', 'users','income_this_month', 'expense_this_month', 'income_last_month','expense_last_month','profit_this_month','profit_last_month','total_saldo'));
    }

    public function store(Request $request)
    {
        // $validator = Validator::make($request->all(), [
        //     'id_data' => ['required', 'string', 'min:5', Rule::unique('keuangan_transaksi')->where('shortname', multi_auth()->shortname)],
        // ]);

        // if ($validator->fails()) {
        //     return response()->json([
        //         'error' => $validator->errors(),
        //     ]);
        // }

        if($request->tipe === 'Pemasukan'){
            $kategori = $request->kategori_income;
        }else{
            $kategori = $request->kategori_expense;
        }
        $transaksi = Transaksi::create([
            'shortname' => multi_auth()->shortname,
            'id_data' => rand(00000, 99999),
            'tanggal' => $request->tanggal,
            'tipe' => $request->tipe,
            'kategori' => $kategori,
            'deskripsi' => $request->deskripsi,
            'nominal' => str_replace('.', '', $request->nominal),
            'metode' => $request->metode,
            'created_by' => multi_auth()->username,
        ]);

       
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $transaksi,
        ]);
    }

    public function update(Request $request, Transaksi $transaksi)
    {
        if($request->tipe === 'Pemasukan'){
            $kategori = $request->kategori_income;
        }else{
            $kategori = $request->kategori_expense;
        }
        $transaksi->update([
            'kategori' => $kategori,
            'deskripsi' => $request->deskripsi,
            'nominal' => str_replace('.', '', $request->nominal),
            'metode' => $request->metode,
        ]);
        activity()
            ->tap(function (Activity $activity) {
                $activity->shortname = multi_auth()->shortname;
            })
            ->event('Update')
            ->log('Update Transaction: ' . $request->deskripsi . '');
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Diupdate',
            'data' => $transaksi,
        ]);
    }

    public function show(Transaksi $transaksi)
    {
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Detail Data',
            'data' => $transaksi,
        ]);
    }

    public function destroy($id)
    {
        $transaksi = Transaksi::findOrFail($id);
        $deskripsi = Transaksi::where('id', $id)->first();
        activity()
            ->tap(function (Activity $activity) {
                $activity->shortname = multi_auth()->shortname;
            })
            ->event('Delete')
            ->log('Delete Transaction: ' . $deskripsi->deskripsi . '');
        $transaksi->delete();
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Dihapus',
            'data' => $transaksi,
        ]);
    }

    public function export(Request $request)
    {
        $periode = Carbon::createFromFormat('F-Y', $request->periode);
        $periode = $periode->translatedFormat('F Y');
        $company = Company::where('shortname', multi_auth()->shortname)->first();
        if ($request->format === 'excel') {
            return Excel::download(new TransaksiExport($request), 'Laporan Keuangan ' . $company->name . ' - ' . $periode . '.xlsx');
        } elseif ($request->format === 'pdf') {
            $month = date('m', strtotime($request->periode));
            $year = date('Y', strtotime($request->periode));
            $transaksi = Transaksi::where('shortname', multi_auth()->shortname)->whereMonth('tanggal', $month)->whereYear('tanggal', $year)->whereNot('created_by','frradius')->get();
            $totalpemasukan = Transaksi::where('shortname', multi_auth()->shortname)->whereMonth('tanggal', $month)->whereYear('tanggal', $year)->where('tipe', 'Pemasukan')->whereNot('created_by','frradius')->sum('nominal');
            $totalpengeluaran = Transaksi::where('shortname', multi_auth()->shortname)->whereMonth('tanggal', $month)->whereYear('tanggal', $year)->where('tipe', 'Pengeluaran')->whereNot('created_by','frradius')->sum('nominal');
            $pdf = Pdf::loadView('backend.keuangan.transaksi.export.pdf', compact('transaksi', 'periode', 'totalpemasukan', 'totalpengeluaran', 'company'))->setPaper('a4', 'landscape'); // Paksa landscape
            return $pdf->download('Laporan Keuangan ' . $company->name . ' - ' . $periode . '.pdf');
        }
    }
}

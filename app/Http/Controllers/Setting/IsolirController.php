<?php

namespace App\Http\Controllers\Setting;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting\Isolir;

class IsolirController extends Controller
{
    public function index()
    {
        $isolir_dhcp = Isolir::where('shortname',multi_auth()->shortname)->whereNot('type','pppoe')->get();
        if (request()->ajax()) {
            $isolir = Isolir::where('shortname',multi_auth()->shortname)->first();
            return response()->json([
                'success' => true,
                'data' => $isolir,
            ]);
        }
        return view('backend.setting.isolir.index',compact('isolir_dhcp'));
    }

    public function update(Request $request, Isolir $isolir)
    {
        if ($request->isolir === 1) {
            $isolir->update([
                'isolir' => $request->isolir,
            ]);
        } else {
            $isolir->update([
                'isolir' => $request->isolir,
            ]);
        }
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $isolir,
        ]);
    }

    public function store(Request $request)
    {
        $isolir = Isolir::create([
            'shortname' => multi_auth()->shortname,
            'isolir' => $request->ip_pool,
            'type' => $request->dhcp_server,
        ]);

        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'data' => $isolir,
        ]);
        
    }

    public function destroy($id)
    {
        $isolir = Isolir::findOrFail($id);
        $isolir->delete();
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Dihapus',
        ]);
    }
    
}

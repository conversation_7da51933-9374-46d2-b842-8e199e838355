<?php

namespace App\Http\Controllers\Setting;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Setting\MidtransController;
use App\Http\Controllers\Setting\DuitkuController;
use App\Models\Setting\Midtrans;
use App\Models\Setting\Mduitku;
use App\Models\Invoice\Invoice;
use App\Models\Keuangan\Transaksi;
use App\Models\Keuangan\TransaksiMidtrans;
use Carbon\Carbon;
use App\Models\Setting\MidtransWithdraw;
use App\Models\Setting\Company;

class PaymentController extends Controller
{

    // midtrans
    public function index()
    {
        $midtrans = Midtrans::where('shortname', multi_auth()->shortname)->first();
        $incomeMonth = TransaksiMidtrans::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->where('created_by', 'midtrans')
            ->whereYear('tanggal', Carbon::today()->year)
            ->whereMonth('tanggal', Carbon::today()->month)
            ->sum('nominal');
        $incomeLastMonth = TransaksiMidtrans::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->where('created_by', 'midtrans')
            ->whereYear('tanggal', Carbon::today()->year)
            ->whereMonth('tanggal', Carbon::today()->subMonth()->month)
            ->sum('nominal');
        $incomeYear = TransaksiMidtrans::where('shortname', multi_auth()->shortname)
            ->where('tipe', 'Pemasukan')
            ->where('created_by', 'midtrans')
            ->whereYear('tanggal', Carbon::today()->year)
            ->sum('nominal');

        $totalIncome = TransaksiMidtrans::where('shortname', multi_auth()->shortname)->where('tipe', 'Pemasukan')->where('created_by', 'midtrans')->sum('nominal');
        $totalWithdraw = MidtransWithdraw::where('shortname', multi_auth()->shortname)->sum('nominal');
        $totalSaldo = $totalIncome - $totalWithdraw;

        if (multi_auth()->license_id == 2) {
            return view('backend.account.limit');
        } else {
            return view('backend.setting.payment.midtrans', compact('midtrans', 'totalSaldo', 'incomeMonth', 'incomeLastMonth', 'incomeYear'));
        }
    }
    public function bayar($id)
    {
        $invoice = Invoice::where('no_invoice', $id)->select('shortname')->first();

        if (!$invoice) {
            return view('backend.invoice.404');
        }

        $shortname = $invoice->shortname;

        $company = Company::where('shortname', $shortname)->select('payment_gateway')->first();

        if ($company->payment_gateway === 'midtrans') {
            return app(MidtransController::class)->bayar($id);
        } else if ($company->payment_gateway === 'duitku') {
            return app(DuitkuController::class)->bayar($id);
        } else {
            return view('backend.invoice.pg-404');
        }
    }
}

<?php

namespace App\Http\Controllers\Setting;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting\Role;
use App\Models\User;
use Illuminate\Support\Str;


class RoleController extends Controller
{
    public function index(Request $request)
    {
        $users = User::where('shortname',multi_auth()->shortname)->whereNot('role','Admin')->get();
        if (request()->ajax()) {
            $role = Role::where('shortname',multi_auth()->shortname)->where('username',$request->username)->first();
            return response()->json([
                'success' => true,
                'data' => $role,
            ]);
        }
        return view('backend.setting.role.index',compact('users'));
    }

    public function store(Request $request)
    {
        $role = Role::where('shortname',multi_auth()->shortname)->where('username',$request->username)->exists();
        if($request->role === 'Operator'){
            $field = Str::after($request->field, 'op_');
        }else if($request->role === 'Kasir'){
            $field = Str::after($request->field, 'kasir_');
        }else if($request->role === 'Teknisi'){
            $field = Str::after($request->field, 'teknisi_');
        }

        if(!$role){
            $role = Role::create([
                'shortname' => multi_auth()->shortname,
                'username' => $request->username,
                $field => $request->value,
            ]);
        }else{
            $role = Role::where('shortname',multi_auth()->shortname)->where('username',$request->username);
            $role->update([
                'username' => $request->username,
                $field => $request->value,
            ]);
        }
        //return response
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
        ]);
    }
}

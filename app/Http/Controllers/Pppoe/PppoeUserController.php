<?php

namespace App\Http\Controllers\Pppoe;

use App\Http\Controllers\Controller;
use App\Models\Pppoe\PppoeUser;
use App\Models\Mapping\Pop;
use App\Models\Mikrotik\Nas;
use App\Models\Pppoe\PppoeProfile;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Models\Mapping\Odp;
use Carbon\Carbon;
use App\Models\Owner\License;
use App\Models\Partnership\Mitra;
use App\Models\Setting\BillingSetting;
use Illuminate\Support\Facades\Process;
use App\Models\Radius\RadiusNas;
use App\Models\Whatsapp\Mpwa;
use App\Models\Whatsapp\Watemplate;
use App\Models\Invoice\Invoice;
use App\Models\Keuangan\Transaksi;
use App\Models\Radius\RadiusSession;
use App\Exports\PppoeUserExport;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\Activitylog\Contracts\Activity;
use App\Imports\PppoeUserImport;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PppoeUserController extends Controller
{
    public function index()
    {
        if (multi_auth()->role === 'Mitra') {
            $totaluser = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->count();
            $totalactive = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->where('status', 1)->count();
            $totalsuspend = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->where('status', 2)->count();
            $totaldisabled = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->where('status', 0)->count();
            $totalpsb = PppoeUser::where('shortname', multi_auth()->shortname)
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->where('mitra_id', multi_auth()->id)
            ->count();
            $totalberhenti = PppoeUser::where('shortname', multi_auth()->shortname)
            ->whereMonth('updated_at', Carbon::now()->month)
            ->whereYear('updated_at', Carbon::now()->year)
            ->where('mitra_id', multi_auth()->id)
            ->where('status',0)
            ->count();
        } else {
            $totalpsb = PppoeUser::where('shortname', multi_auth()->shortname)
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
            $totalberhenti = PppoeUser::where('shortname', multi_auth()->shortname)
            ->whereMonth('updated_at', Carbon::now()->month)
            ->whereYear('updated_at', Carbon::now()->year)
            ->where('status',0)
            ->count();
            $totaluser = PppoeUser::where('shortname', multi_auth()->shortname)->count();
            $totalactive = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 1)->count();
            $totalsuspend = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 2)->count();
            $totaldisabled = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 0)->count();
        }
        $areas = Pop::where('shortname', multi_auth()->shortname)->select('id', 'kode_area', 'deskripsi')->orderBy('kode_area', 'desc')->get();
        $nas = Nas::where('shortname', multi_auth()->shortname)->select('ip_router', 'name')->get();
        if (multi_auth()->role !== 'Mitra') {
            $profiles = PppoeProfile::where('shortname', multi_auth()->shortname)->where('status', 1)->select('id', 'name', 'price')->get();
        } else {
            $id = json_decode(multi_auth()->profile, true); // true biar hasilnya array, bukan object
            if ($id !== null) {
                $profiles = PppoeProfile::where('shortname', multi_auth()->shortname)->whereIn('id', $id)->where('status', 1)->select('id', 'name', 'price')->get();
            } else {
                $profiles = [];
            }
        }
        $mitras = Mitra::where('shortname', multi_auth()->shortname)->where('status', 1)->select('id', 'name', 'id_mitra')->get();

        if (request()->ajax()) {
            if (multi_auth()->role === 'Mitra') {
                $users = PppoeUser::query()->where('user_pppoe.shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->with('mnas', 'session', 'rprofile', 'rarea', 'rodp', 'rmitra');
            } else {
                $users = PppoeUser::query()
                    ->where('user_pppoe.shortname', multi_auth()->shortname)
                    ->when(request('area'), function ($query, $area) {
                        $query->where('kode_area', $area); // atau sesuaikan dengan nama kolom relasi jika pakai relasi rarea
                    })
                    ->when(request('status'), function ($query, $status) {
                        $query->where('status', $status);
                    })
                    ->when(request('nas'), function ($query, $nas) {
                        $query->where('nas', $nas); // atau sesuaikan dengan relasi `mnas`
                    })
                    ->when(request('mitra'), function ($query, $mitra) {
                        $query->where('mitra_id', $mitra); // atau relasi `rmitra`
                    })
                    ->with('mnas', 'session', 'rprofile', 'rarea', 'rodp', 'rmitra');
            }
            return DataTables::of($users)
                ->addIndexColumn()
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="row-cb form-check-input kt-checkbox kt-checkbox-sm" id="checkbox_row' . $row->id . '" value="' . $row->id . '"/>';
                })
                ->rawColumns(['action', 'checkbox'])
                ->addColumn('profile_name', function ($row) {
                    return $row->rprofile->name;
                })
                ->addColumn('inet', function ($row) {
                    return $row->session->session_id;
                })
                ->addColumn('profile', function ($row) {
                    return $row->rprofile->name;
                })
                ->addColumn('mitra', function ($row) {
                    return $row->rmitra->name;
                })
                ->toJson();
        }
        return view('backend.pppoe.user.index', compact('areas', 'nas', 'profiles', 'mitras', 'totaluser', 'totalactive', 'totalsuspend', 'totaldisabled','totalpsb','totalberhenti'));
    }
    public function getKodeOdp(Request $request)
    {
        $data['odp'] = Odp::where('shortname', multi_auth()->shortname)
            ->where('kode_area_id', $request->kode_area_id)
            ->orderBy('kode_odp')
            ->get(['kode_odp']);
        return response()->json($data);
    }

    public function getPrice(Request $request)
    {
        $data = PppoeProfile::where('shortname', multi_auth()->shortname)
            ->where('id', $request->id)
            ->get(['price']);
        return response()->json($data);
    }

    public function show(PppoeUser $user)
    {
        $data = PppoeUser::with('mnas', 'rprofile', 'rarea', 'rodp')->find($user->id);
        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => ['required', 'string', 'min:1', 'max:255', Rule::unique('frradius_auth.user_pppoe')->where('shortname', multi_auth()->shortname)],
            'profile' => 'required|string',
            'full_name' => 'required|string|min:2',
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    'errors' => $validator->errors(),
                ],
                422,
            );
        }

        if ($request->mitra_id == null) {
            $mitra_id = 0;
        } else {
            $mitra_id = $request->mitra_id;
        }

        // if (in_array(multi_auth()->role, ['Admin', 'Teknisi'])) {
        //     if ($request->status === null) {
        //         $status = 0;
        //     } else {
        //         $status = $request->status;
        //     }
        // } else {
        //     $status = 0;
        // }
        if ($request->biaya_pasang !== null || $request->biaya_pasang != 0) {
            $status = 0;
        } else {
            $status = 1;
        }
        if ($request->type === 'dhcp') {
            $password = null;
        } else {
            $password = $request->password;
        }

        if (multi_auth()->role !== 'Mitra') {
            if (PppoeUser::where('shortname', multi_auth()->shortname)->count() >= License::where('id', multi_auth()->license_id)->select('limit_pppoe')->first()->limit_pppoe) {
                return response()->json([
                    'errors' => 'Maaf lisensi anda sudah limit, silakan upgrade!',
                ]);
            }
        }

        if ($request->option_billing === '1') {
            $today = Carbon::now()->format('Y-m-d');
            $cek_reg_date = Carbon::createFromFormat('Y-m-d', $today)->subMonthsWithNoOverflow(2)->toDateString();
            $cek_reg_date_bc = Carbon::createFromFormat('Y-m-d', $today)->startOfMonth()->subMonthsWithNoOverflow(2)->toDateString();
            $tgl = date('d', strtotime($request->reg_date));
            $no_invoice = date('m') . rand(10000000, 99999999);

            if ($request->reg_date < $cek_reg_date) {
                return response()->json([
                    'errors' => 'Maaf gagal membuat user, silakan ubah tanggal aktif!',
                ]);
            } elseif ($request->payment_type === 'Pascabayar' && $request->billing_period === 'Billing Cycle' && $request->reg_date < $cek_reg_date_bc) {
                return response()->json([
                    'success' => 'Maaf gagal membuat user, silakan ubah tanggal aktif!',
                ]);

                // SETTING PARAMETER

                // PRABAYAR PAID
            } elseif ($request->payment_type === 'Prabayar' && $request->payment_status === 'paid' && $request->reg_date >= $cek_reg_date) {
                $next_due = Carbon::createFromFormat('Y-m-d', $request->reg_date)->addMonthsWithNoOverflow(1);
                $next_invoice = Carbon::createFromFormat('Y-m-d', $request->reg_date)->addMonthsWithNoOverflow(1);
                $price = $request->amount;
                $total = $request->payment_total;
                $period = Carbon::createFromFormat('Y-m-d', $request->reg_date);
                $awal = date('d/m/Y', strtotime($request->reg_date));
                $akhir = date('d/m/Y', strtotime('+1 month', strtotime($request->reg_date)));

                // PRABAYAR UNPAID
            } elseif ($request->payment_type === 'Prabayar' && $request->payment_status === 'unpaid' && $request->reg_date > $cek_reg_date) {
                $next_due = $request->reg_date;
                $next_invoice = Carbon::createFromFormat('Y-m-d', $request->reg_date)->addMonthsWithNoOverflow(1);
                $price = $request->amount;
                $period = Carbon::createFromFormat('Y-m-d', $request->reg_date);
                $awal = date('d/m/Y', strtotime($request->reg_date));
                $akhir = date('d/m/Y', strtotime('+1 month', strtotime($request->reg_date)));
                // PASCABAYAR FIXED DATE
            } elseif ($request->payment_type === 'Pascabayar' && $request->billing_period === 'Fixed Date' && $request->reg_date > $cek_reg_date) {
                $next_due = Carbon::createFromFormat('Y-m-d', $request->reg_date)->addMonthsWithNoOverflow(1);
                $next_invoice = Carbon::createFromFormat('Y-m-d', $request->reg_date)->addMonthsWithNoOverflow(1);
                $period = Carbon::createFromFormat('Y-m-d', $request->reg_date);
                $awal = date('d/m/Y', strtotime($request->reg_date));
                $akhir = date('d/m/Y', strtotime('+1 month', strtotime($request->reg_date)));

                // PASCABAYAR BILLING CYCLE PRORATE
            } elseif ($request->payment_type === 'Pascabayar' && $request->billing_period === 'Billing Cycle' && $request->reg_date < $cek_reg_date_bc) {
                $due_bc = BillingSetting::where('shortname', multi_auth()->shortname)->select('due_bc')->first();
                $next_due = Carbon::createFromFormat('Y-m-d', $request->reg_date)->setDay($due_bc->due_bc)->addMonths(1);
                $next_invoice = Carbon::createFromFormat('Y-m-d', $request->reg_date)->startOfMonth()->addMonths(1);
                $price = $request->amount;
                $period = Carbon::createFromFormat('Y-m-d', $request->reg_date)->toDateString();
                $next_invoice = Carbon::createFromFormat('Y-m-d', $request->reg_date)->startOfMonth()->addMonthsWithNoOverflow(1)->toDateString();
                $akhir_day = Carbon::createFromFormat('Y-m-d', $request->reg_date)->endOfMonth()->toDateString();

                $awal = date('d/m/Y', strtotime($request->reg_date));
                $akhir = date('d/m/Y', strtotime($akhir_day));

                // MASIH PR DISINI
                $jml_day = Carbon::createFromFormat('Y-m-d', $request->reg_date)->month()->daysInMonth;
                $jml_usage = Carbon::parse($request->reg_date)->diffInDays($akhir_day);
                $daily_price0 = $price / $jml_day;
                $daily_price = number_format($daily_price0, 0, '.', '');
                $prorate = $jml_usage * $daily_price;
                $total = number_format($prorate, 0, '.', '');

                // PASCABAYAR BILILNG CYCLE NON PRORATE
            } elseif ($request->payment_type === 'Pascabayar' && $request->billing_period === 'Billing Cycle' && $request->reg_date > $cek_reg_date_bc) {
                $due_bc = BillingSetting::where('shortname', multi_auth()->shortname)->select('due_bc')->first();
                $next_due = Carbon::createFromFormat('Y-m-d', $request->reg_date)->setDay($due_bc->due_bc)->addMonths(1);
                $next_invoice = Carbon::createFromFormat('Y-m-d', $request->reg_date)->startOfMonth()->addMonths(1);
                $period = Carbon::createFromFormat('Y-m-d', $request->reg_date);
                $awal = date('d/m/Y', strtotime($request->reg_date));
                $akhir = date('d/m/Y', strtotime('+1 month', strtotime($request->reg_date)));
            }

            $user = PppoeUser::create([
                'shortname' => multi_auth()->shortname,
                'type' => $request->type,
                'username' => $request->username,
                'value' => $password,
                'profile' => $request->profile,
                'nas' => $request->nas,
                'lock_mac' => 0,
                'mac' => $request->mac,
                'ip_address' => $request->ip_address,
                'full_name' => $request->full_name,
                'id_pelanggan' => $request->id_pelanggan,
                'password_pelanggan' => $request->id_pelanggan,
                'profile_id' => $request->profile_id,
                'kode_area' => $request->kode_area,
                'kode_odp' => $request->kode_odp,
                'mitra_id' => $mitra_id,
                'wa' => $request->wa,
                'address' => $request->address,
                'payment_type' => $request->payment_type,
                'payment_status' => $request->payment_status,
                'billing_period' => $request->billing_period,
                'ppn' => $request->ppn,
                'discount' => $request->discount,
                'reg_date' => $request->reg_date,
                'next_due' => $next_due,
                'next_invoice' => $next_invoice,
                'tgl' => $tgl,
                'status' => $status,
                'created_by' => multi_auth()->username ?? multi_auth()->name,
            ]);

            if ($request->biaya_pasang !== null || $request->biaya_pasang != 0) {
                $no_invoice_psb = date('m') . rand(10000000, 99999999);
                $invoice = Invoice::create([
                    'shortname' => multi_auth()->shortname,
                    'id_pelanggan' => $user->id,
                    'no_invoice' => $no_invoice_psb,
                    'item' => "Biaya Pemasangan Baru: $user->id_pelanggan - $user->username | $user->profile",
                    'price' => $request->biaya_pasang,
                    'invoice_date' => $user->reg_date,
                    'due_date' => $user->reg_date,
                    'period' => $period,
                    'subscribe' => $awal . ' ' . 's/d' . ' ' . $akhir,
                    'payment_type' => $user->payment_type,
                    'billing_period' => $user->billing_period,
                    'payment_url' => multi_auth()->domain . '/pay/' . $no_invoice_psb,
                    'status' => 'unpaid',
                ]);
            }

            // PRABAYAR PAID
            if ($request->payment_type === 'Prabayar' && $request->payment_status === 'paid' && $request->reg_date >= $cek_reg_date) {
                $invoice = Invoice::create([
                    'shortname' => multi_auth()->shortname,
                    'id_pelanggan' => $user->id,
                    'no_invoice' => $no_invoice,
                    'item' => "Internet: $user->id_pelanggan | $user->profile",
                    'price' => $price,
                    'ppn' => $user->ppn,
                    'discount' => $user->discount,
                    'invoice_date' => $user->reg_date,
                    'due_date' => $user->reg_date,
                    'period' => $period,
                    'subscribe' => $awal . ' ' . 's/d' . ' ' . $akhir,
                    'reg_date' => $user->reg_date,
                    'next_due' => $next_due,
                    'payment_type' => $user->payment_type,
                    'billing_period' => $user->billing_period,
                    'payment_url' => multi_auth()->domain . '/pay/' . $no_invoice,
                    'paid_date' => $user->reg_date,
                    'status' => 'paid',
                    'mitra_id' => $user->mitra_id,
                ]);

                $transaksi = Transaksi::create([
                    'shortname' => multi_auth()->shortname,
                    'id_data' => $invoice->id,
                    'tipe' => 'Pemasukan',
                    'kategori' => 'Invoice',
                    'deskripsi' => "PSB Payment #$invoice->no_invoice a.n $user->full_name",
                    'nominal' => $total,
                    'tanggal' => Carbon::now(),
                    'metode' => 'Cash',
                    'created_by' => multi_auth()->username ?? multi_auth()->name,
                ]);
                // PRABAYAR UNPAID
            } elseif ($request->payment_type === 'Prabayar' && $request->payment_status === 'unpaid' && $request->reg_date > $cek_reg_date) {
                $invoice = Invoice::create([
                    'shortname' => multi_auth()->shortname,
                    'id_pelanggan' => $user->id,
                    'no_invoice' => $no_invoice,
                    'item' => "Internet: $user->id_pelanggan | $user->profile",
                    'price' => $price,
                    'ppn' => $user->ppn,
                    'discount' => $user->discount,
                    'invoice_date' => $user->reg_date,
                    'due_date' => $user->reg_date,
                    'period' => $period,
                    'subscribe' => $awal . ' ' . 's/d' . ' ' . $akhir,
                    'reg_date' => $user->reg_date,
                    'next_due' => $next_due,
                    'payment_type' => $user->payment_type,
                    'billing_period' => $user->billing_period,
                    'payment_url' => multi_auth()->domain . '/pay/' . $no_invoice,
                    'paid_date' => $user->reg_date,
                    'status' => 'unpaid',
                    'mitra_id' => $user->mitra_id,
                ]);
            }
        } else {
            $user = PppoeUser::create([
                'shortname' => multi_auth()->shortname,
                'type' => $request->type,
                'username' => $request->username,
                'value' => $password,
                'profile' => $request->profile,
                'nas' => $request->nas,
                'lock_mac' => 0,
                'mac' => $request->mac,
                'ip_address' => $request->ip_address,
                'status' => 1,
                'full_name' => $request->full_name,
                'id_pelanggan' => $request->id_pelanggan,
                'profile_id' => $request->profile_id,
                'kode_area' => $request->kode_area,
                'kode_odp' => $request->kode_odp,
                'mitra_id' => $mitra_id,
                'wa' => $request->wa,
                'address' => $request->address,
                'status' => $status,
                'created_by' => multi_auth()->username ?? multi_auth()->name,
            ]);
        }

        if (multi_auth()->role === 'Mitra') {
            $totaluser = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->count();
            $totalactive = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->where('status', 1)->count();
            $totalsuspend = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->where('status', 2)->count();
            $totaldisabled = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->where('status', 0)->count();
        } else {
            $totaluser = PppoeUser::where('shortname', multi_auth()->shortname)->count();
            $totalactive = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 1)->count();
            $totalsuspend = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 2)->count();
            $totaldisabled = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 0)->count();
        }

        if (multi_auth()->role !== 'Mitra') {
            activity()
                ->tap(function (Activity $activity) {
                    $activity->shortname = multi_auth()->shortname;
                })
                ->event('Create')
                ->log('Create New User PPPoE: ' . $request->username . '');
        }
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Disimpan',
            'totaluser' => $totaluser,
            'totalactive' => $totalactive,
            'totalsuspend' => $totalsuspend,
            'totaldisabled' => $totaldisabled,
        ]);
    }

    public function update(Request $request, PppoeUser $user)
    {
        // $validator = Validator::make($request->all(), [
        //     'username' => 'required|string|min:5',
        //     'password' => 'required|string|min:2',
        //     'profile' => 'required',
        // ]);

        // if ($validator->fails()) {
        //     return response()->json([
        //         'error' => $validator->errors(),
        //     ]);
        // }

        if ($request->type === 'dhcp') {
            $password = null;
        } else {
            $password = $request->password;
        }

        if ($request->mitra_id == null) {
            $mitra_id = 0;
        } else {
            $mitra_id = $request->mitra_id;
        }
        if (multi_auth()->role === 'Admin') {
            if ($request->option_billing === '1') {
                // PRABAYAR FIXED DATE
                if ($request->payment_type === 'Prabayar' && $request->billing_period === 'Fixed Date') {
                    $next_due = $request->next_due;
                    $next_invoice = $request->next_due;
                    if ($request->reg_date === null) {
                        $reg_date = Carbon::createFromFormat('Y-m-d', $request->next_due)->subMonthsWithNoOverflow(1);
                    } else {
                        $reg_date = $request->reg_date;
                    }
                    // PRABAYAR RENEWABLE
                } elseif ($request->payment_type === 'Prabayar' && $request->billing_period === 'Renewable') {
                    $next_due = $request->next_due;
                    $next_invoice = $request->next_due;
                    if ($request->reg_date === null) {
                        $reg_date = Carbon::createFromFormat('Y-m-d', $request->next_due)->subMonthsWithNoOverflow(1);
                    } else {
                        $reg_date = $request->reg_date;
                    }
                    // PASCABAYAR FIXED DATE
                } elseif ($request->payment_type === 'Pascabayar' && $request->billing_period === 'Fixed Date') {
                    $next_due = $request->next_due;
                    $next_invoice = $request->next_due;
                    if ($request->reg_date === null) {
                        $reg_date = Carbon::createFromFormat('Y-m-d', $request->next_due)->subMonthsWithNoOverflow(1);
                    } else {
                        $reg_date = $request->reg_date;
                    }
                    // PASCABAYAR BILILNG CYCLE
                } elseif ($request->payment_type === 'Pascabayar' && $request->billing_period === 'Billing Cycle') {
                    $due_bc = BillingSetting::where('shortname', multi_auth()->shortname)->select('due_bc')->first();
                    $next_due = Carbon::createFromFormat('Y-m-d', $request->next_due)->setDay($due_bc->due_bc);
                    $next_invoice = Carbon::createFromFormat('Y-m-d', $request->next_due)->startOfMonth();

                    if ($request->reg_date === null) {
                        $reg_date = Carbon::createFromFormat('Y-m-d', $request->next_due)->subMonthsWithNoOverflow(1)->startOfMonth();
                    } else {
                        $reg_date = $request->reg_date;
                    }
                } else {
                    $next_due = null;
                    $next_invoice = null;
                    $reg_date = null;
                }

                $invoice = Invoice::where('id_pelanggan', $user)->where('status', 'unpaid')->count();
                if ($invoice === 0) {
                    $user->update([
                        'type' => $request->type,
                        'username' => $request->username,
                        'value' => $password,
                        'profile' => $request->profile,
                        'nas' => $request->nas,
                        'ip_address' => $request->ip,
                        // 'lock_mac' => $request->lock_mac,
                        // 'mac' => $request->mac,
                        'full_name' => $request->full_name,
                        'profile_id' => $request->profile_id,
                        'kode_area' => $request->kode_area,
                        'kode_odp' => $request->kode_odp,
                        'mitra_id' => $mitra_id,
                        'wa' => $request->wa,
                        'address' => $request->address,
                        'ppn' => $request->ppn,
                        'discount' => $request->discount,
                        'payment_type' => $request->payment_type,
                        'billing_period' => $request->billing_period,
                        'next_due' => $next_due,
                        'next_invoice' => $next_invoice,
                        'reg_date' => $reg_date,
                    ]);
                } else {
                    $user->update([
                        'type' => $request->type,
                        'username' => $request->username,
                        'value' => $password,
                        'profile' => $request->profile,
                        'nas' => $request->nas,
                        'ip_address' => $request->ip,
                        // 'lock_mac' => $request->lock_mac,
                        // 'mac' => $request->mac,
                        'full_name' => $request->full_name,
                        'profile_id' => $request->profile_id,
                        'kode_area' => $request->kode_area,
                        'kode_odp' => $request->kode_odp,
                        'mitra_id' => $mitra_id,
                        'wa' => $request->wa,
                        'address' => $request->address,
                        // 'ppn' => $request->ppn,
                        // 'discount' => $request->discount,
                        // 'payment_type' => $request->payment_type,
                        // 'billing_period' => $request->billing_period,
                        // 'next_due' => $next_due,
                        // 'next_invoice' => $next_invoice,
                        // 'reg_date' => $reg_date,
                    ]);
                }
            } else {
                $user->update([
                    'type' => $request->type,
                    'username' => $request->username,
                    'value' => $password,
                    'profile' => $request->profile,
                    'nas' => $request->nas,
                    'ip_address' => $request->ip,
                    // 'lock_mac' => $request->lock_mac,
                    // 'mac' => $request->mac,
                    'full_name' => $request->full_name,
                    'profile_id' => $request->profile_id,
                    'kode_area' => $request->kode_area,
                    'kode_odp' => $request->kode_odp,
                    'mitra_id' => $mitra_id,
                    'wa' => $request->wa,
                    'address' => $request->address,
                    'payment_type' => null,
                    'billing_period' => null,
                    'next_due' => null,
                    'next_invoice' => null,
                ]);
            }
        } else {
            $user->update([
                'type' => $request->type,
                'username' => $request->username,
                'value' => $password,
                // 'profile' => $request->profile,
                // 'nas' => $request->nas,
                'ip_address' => $request->ip,
                // 'lock_mac' => $request->lock_mac,
                // 'mac' => $request->mac,
                'full_name' => $request->full_name,
                // 'profile_id' => $request->profile_id,
                'kode_area' => $request->kode_area,
                'kode_odp' => $request->kode_odp,
                // 'mitra_id' => $mitra_id,
                'wa' => $request->wa,
                'address' => $request->address,
            ]);
        }

        if (multi_auth()->role === 'Mitra') {
            $totaluser = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->count();
            $totalactive = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->where('status', 1)->count();
            $totalsuspend = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->where('status', 2)->count();
            $totaldisabled = PppoeUser::where('shortname', multi_auth()->shortname)->where('mitra_id', multi_auth()->id)->where('status', 0)->count();
        } else {
            $totaluser = PppoeUser::where('shortname', multi_auth()->shortname)->count();
            $totalactive = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 1)->count();
            $totalsuspend = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 2)->count();
            $totaldisabled = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 0)->count();
        }

        if (multi_auth()->role !== 'Mitra') {
            activity()
                ->tap(function (Activity $activity) {
                    $activity->shortname = multi_auth()->shortname;
                })
                ->event('Update')
                ->log('Update User PPPoE: ' . $request->username . '');
        }
        return response()->json([
            'success' => true,
            'message' => 'Data Berhasil Diupdate',
            'totaluser' => $totaluser,
            'totalactive' => $totalactive,
            'totalsuspend' => $totalsuspend,
            'totaldisabled' => $totaldisabled,
        ]);
    }

    public function suspend(Request $request)
    {
        // SSH configuration
        $sshUser = env('IP_RADIUS_USERNAME');
        $sshHost = env('IP_RADIUS_SERVER');
        $sshOptions = ['-o', 'BatchMode=yes', '-o', 'StrictHostKeyChecking=no'];
        $sshOptionsString = implode(' ', $sshOptions);

        // Fetch users to disable
        $userList = PppoeUser::whereIn('id', $request->ids)->select('username', 'nas')->get();

        // Preload default NAS entries for users without specific NAS
        $defaultNasList = RadiusNas::where('shortname', multi_auth()->shortname)->get(['nasname', 'secret']);

        foreach ($userList as $user) {
            // Determine NAS entries for this user
            $nasItems = $user->nas ? RadiusNas::where('nasname', $user->nas)->get(['nasname', 'secret']) : $defaultNasList;

            foreach ($nasItems as $nas) {
                // Build radclient disconnect command
                $usernameArg = escapeshellarg("User-Name = '{$user->username}'");
                $radclientCmd = "echo {$usernameArg} | radclient -r 1 {$nas->nasname}:3799 disconnect {$nas->secret}";

                // Build SSH command with options
                $sshCommand = "ssh {$sshOptionsString} {$sshUser}@{$sshHost} \"{$radclientCmd}\"";
                $process = Process::run($sshCommand);

                // Log execution details
                // Log::info('SSH disconnect executed', [
                //     'command' => $sshCommand,
                //     'output' => $process->output(),
                //     'success' => $process->successful(),
                // ]);

                if (!$process->successful()) {
                    // Log::error('SSH disconnect failed', [
                    //     'command' => $sshCommand,
                    //     'error' => $process->errorOutput(),
                    // ]);
                }
            }

            $notif_sm = BillingSetting::where('shortname', multi_auth()->shortname)->first()->notif_sm;
            if ($notif_sm === 1) {
                $mpwa = Mpwa::where('shortname', multi_auth()->shortname)->first();
                $users_pppoe = PppoeUser::whereIn('id', $request->ids)->with('rprofile')->get();
                foreach ($users_pppoe as $row) {
                    if ($row->wa != null) {
                        $amount_ppn = ($row->rprofile->price * $row->ppn) / 100;
                        $amount_discount = $row->discount;
                        $total = $row->rprofile->price + $amount_ppn - $amount_discount;
                        $shortcode = ['[nama_lengkap]', '[id_pelanggan]', '[username]', '[password]', '[alamat]', '[paket_internet]', '[harga]', '[ppn]', '[discount]', '[total]', '[tipe_pembayaran]', '[siklus_tagihan]', '[tgl_aktif]', '[jth_tempo]'];
                        $source = [$row->full_name, $row->id_pelanggan, $row->username, $row->password, $row->address, $row->profile, number_format($row->rprofile->price, 0, '.', '.'), $row->ppn, number_format($row->discount, 0, '.', '.'), number_format($total, 0, '.', '.'), $row->payment_type, $row->billing_period, date('d/m/Y', strtotime($row->reg_date)), date('d/m/Y', strtotime($row->next_due))];
                        $template = Watemplate::where('shortname', $row->shortname)->first()->account_suspend;
                        $message = str_replace($shortcode, $source, $template);
                        $message_format = str_replace('<br>', "\n", $message);

                        try {
                            $curl = curl_init();
                            $data = [
                                'api_key' => $mpwa->api_key,
                                'sender' => $mpwa->sender,
                                'number' => $row->wa,
                                'message' => $message_format,
                            ];
                            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
                            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
                            curl_setopt($curl, CURLOPT_URL, 'https://' . $mpwa->mpwa_server . '/send-message');
                            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
                            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
                            $response = curl_exec($curl);
                            curl_close($curl);
                            // $result = json_decode($response, true);
                        } catch (\Exception $e) {
                            return $e->getMessage();
                        }
                    }
                }
            }

            // Record activity per user
            activity()
                ->tap(fn(Activity $activity) => ($activity->shortname = multi_auth()->shortname))
                ->event('Update')
                ->log("Suspend User PPPoE: {$user->username}");
        }

        // Update user statuses in bulk
        PppoeUser::whereIn('id', $request->ids)->update(['status' => 2]);

        // Retrieve updated totals
        $totals = PppoeUser::where('shortname', multi_auth()->shortname)->selectRaw('count(*) as totaluser')->selectRaw('sum(status = 1) as totalactive')->selectRaw('sum(status = 2) as totalsuspend')->selectRaw('sum(status = 0) as totaldisabled')->first();

        // Return response
        return response()->json([
            'success' => true,
            'message' => 'Pelanggan Berhasil Disuspend',
            'data' => $userList,
            'totaluser' => $totals->totaluser,
            'totalactive' => $totals->totalactive,
            'totalsuspend' => $totals->totalsuspend,
            'totaldisabled' => $totals->totaldisabled,
        ]);
    }

    public function disable(Request $request)
    {
        // SSH configuration
        $sshUser = env('IP_RADIUS_USERNAME');
        $sshHost = env('IP_RADIUS_SERVER');
        $sshOptions = ['-o', 'BatchMode=yes', '-o', 'StrictHostKeyChecking=no'];
        $sshOptionsString = implode(' ', $sshOptions);

        // Fetch users to disable
        $userList = PppoeUser::whereIn('id', $request->ids)->select('username', 'nas')->get();

        // Preload default NAS entries for users without specific NAS
        $defaultNasList = RadiusNas::where('shortname', multi_auth()->shortname)->get(['nasname', 'secret']);

        foreach ($userList as $user) {
            // Determine NAS entries for this user
            $nasItems = $user->nas ? RadiusNas::where('nasname', $user->nas)->get(['nasname', 'secret']) : $defaultNasList;

            foreach ($nasItems as $nas) {
                // Build radclient disconnect command
                $usernameArg = escapeshellarg("User-Name = '{$user->username}'");
                $radclientCmd = "echo {$usernameArg} | radclient -r 1 {$nas->nasname}:3799 disconnect {$nas->secret}";

                // Build SSH command with options
                $sshCommand = "ssh {$sshOptionsString} {$sshUser}@{$sshHost} \"{$radclientCmd}\"";
                $process = Process::run($sshCommand);

                // Log execution details
                // Log::info('SSH disconnect executed', [
                //     'command' => $sshCommand,
                //     'output' => $process->output(),
                //     'success' => $process->successful(),
                // ]);

                if (!$process->successful()) {
                    // Log::error('SSH disconnect failed', [
                    //     'command' => $sshCommand,
                    //     'error' => $process->errorOutput(),
                    // ]);
                }
            }

            // Record activity per user
            activity()
                ->tap(fn(Activity $activity) => ($activity->shortname = multi_auth()->shortname))
                ->event('Update')
                ->log("Disable User PPPoE: {$user->username}");
        }

        // Update user statuses in bulk
        PppoeUser::whereIn('id', $request->ids)->update(['status' => 0]);

        // Retrieve updated totals
        $totals = PppoeUser::where('shortname', multi_auth()->shortname)->selectRaw('count(*) as totaluser')->selectRaw('sum(status = 1) as totalactive')->selectRaw('sum(status = 2) as totalsuspend')->selectRaw('sum(status = 0) as totaldisabled')->first();

        // Return response
        return response()->json([
            'success' => true,
            'message' => 'Pelanggan Berhasil Dinonaktifkan',
            'data' => $userList,
            'totaluser' => $totals->totaluser,
            'totalactive' => $totals->totalactive,
            'totalsuspend' => $totals->totalsuspend,
            'totaldisabled' => $totals->totaldisabled,
        ]);
    }

    public function enable(Request $request)
    {
        // SSH configuration
        $sshUser = env('IP_RADIUS_USERNAME');
        $sshHost = env('IP_RADIUS_SERVER');
        $sshOptions = ['-o', 'BatchMode=yes', '-o', 'StrictHostKeyChecking=no'];
        $sshOptionsString = implode(' ', $sshOptions);

        // Fetch users to enable
        $userList = PppoeUser::whereIn('id', $request->ids)->select('username', 'nas')->get();

        // Preload default NAS entries for users without specific NAS
        $defaultNasList = RadiusNas::where('shortname', multi_auth()->shortname)->get(['nasname', 'secret']);

        foreach ($userList as $user) {
            // Determine NAS entries for this user
            $nasItems = $user->nas ? RadiusNas::where('nasname', $user->nas)->get(['nasname', 'secret']) : $defaultNasList;

            foreach ($nasItems as $nas) {
                // Build radclient disconnect command (as per original flow)
                $usernameArg = escapeshellarg("User-Name = '{$user->username}'");
                $radclientCmd = "echo {$usernameArg} | radclient -r 1 {$nas->nasname}:3799 disconnect {$nas->secret}";

                // Build SSH command with options
                $sshCommand = "ssh {$sshOptionsString} {$sshUser}@{$sshHost} \"{$radclientCmd}\"";
                $process = Process::run($sshCommand);

                // Log execution details
                // Log::info('SSH reconnect executed', [
                //     'command' => $sshCommand,
                //     'output' => $process->output(),
                //     'success' => $process->successful(),
                // ]);

                if (!$process->successful()) {
                    // Log::error('SSH reconnect failed', [
                    //     'command' => $sshCommand,
                    //     'error' => $process->errorOutput(),
                    // ]);
                }
            }

            $notif_sm = BillingSetting::where('shortname', multi_auth()->shortname)->first()->notif_sm;
            if ($notif_sm === 1) {
                $mpwa = Mpwa::where('shortname', multi_auth()->shortname)->first();
                $users_pppoe = PppoeUser::whereIn('id', $request->ids)->with('rprofile')->get();
                foreach ($users_pppoe as $row) {
                    if ($row->wa != null) {
                        $amount_ppn = ($row->rprofile->price * $row->ppn) / 100;
                        $amount_discount = $row->discount;
                        $total = $row->rprofile->price + $amount_ppn - $amount_discount;
                        $shortcode = ['[nama_lengkap]', '[id_pelanggan]', '[username]', '[password]', '[alamat]', '[paket_internet]', '[harga]', '[ppn]', '[discount]', '[total]', '[tipe_pembayaran]', '[siklus_tagihan]', '[tgl_aktif]', '[jth_tempo]'];
                        $source = [$row->full_name, $row->id_pelanggan, $row->username, $row->password, $row->address, $row->profile, number_format($row->rprofile->price, 0, '.', '.'), $row->ppn, number_format($row->discount, 0, '.', '.'), number_format($total, 0, '.', '.'), $row->payment_type, $row->billing_period, date('d/m/Y', strtotime($row->reg_date)), date('d/m/Y', strtotime($row->next_due))];
                        $template = Watemplate::where('shortname', $row->shortname)->first()->account_active;
                        $message = str_replace($shortcode, $source, $template);
                        $message_format = str_replace('<br>', "\n", $message);

                        try {
                            $curl = curl_init();
                            $data = [
                                'api_key' => $mpwa->api_key,
                                'sender' => $mpwa->sender,
                                'number' => $row->wa,
                                'message' => $message_format,
                            ];
                            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
                            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
                            curl_setopt($curl, CURLOPT_URL, 'https://' . $mpwa->mpwa_server . '/send-message');
                            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
                            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
                            $response = curl_exec($curl);
                            curl_close($curl);
                            // $result = json_decode($response, true);
                        } catch (\Exception $e) {
                            return $e->getMessage();
                        }
                    }
                }
            }

            // Record activity per user
            activity()
                ->tap(fn(Activity $activity) => ($activity->shortname = multi_auth()->shortname))
                ->event('Update')
                ->log("Enable User PPPoE: {$user->username}");
        }

        // Update user statuses in bulk
        PppoeUser::whereIn('id', $request->ids)->update(['status' => 1]);

        // Retrieve updated totals
        $totals = PppoeUser::where('shortname', multi_auth()->shortname)->selectRaw('count(*) as totaluser')->selectRaw('sum(status = 1) as totalactive')->selectRaw('sum(status = 2) as totalsuspend')->selectRaw('sum(status = 0) as totaldisabled')->first();

        // Return response
        return response()->json([
            'success' => true,
            'message' => 'User Berhasil Diaktifkan',
            'data' => $userList,
            'totaluser' => $totals->totaluser,
            'totalactive' => $totals->totalactive,
            'totalsuspend' => $totals->totalsuspend,
            'totaldisabled' => $totals->totaldisabled,
        ]);
    }

    public function kick(Request $request)
    {
        $ssh_user = env('IP_RADIUS_USERNAME');
        $ssh_host = env('IP_RADIUS_SERVER');
        $sshOptions = ['-o', 'BatchMode=yes', '-o', 'StrictHostKeyChecking=no'];
        $sshOptionsString = implode(' ', $sshOptions);

        $user = PppoeUser::where('shortname', multi_auth()->shortname)->where('username', $request->username)->select('username', 'nas')->first();
        if ($user->nas === null) {
            $nas = RadiusNas::where('shortname', multi_auth()->shortname)->select('nasname', 'secret')->get();
            foreach ($nas as $item) {
                $escapedUsername = escapeshellarg("User-Name = '{$user->username}'");
                $command = "echo $escapedUsername | radclient -r 1 {$item['nasname']}:3799 disconnect {$item['secret']}";
                $ssh_command = "ssh {$sshOptionsString} {$ssh_user}@{$ssh_host} \"{$command}\"";
                $process = Process::run($ssh_command);
            }
        } else {
            $secret = RadiusNas::where('nasname', $user->nas)->select('secret')->first();
            $username = escapeshellarg("User-Name = '{$user->username}'");
            $command = "echo $username | radclient -r 1 {$user->nas}:3799 disconnect {$secret->secret}";
            $ssh_command = "ssh {$sshOptionsString} {$ssh_user}@{$ssh_host} \"{$command}\"";
            $process = Process::run($ssh_command);
        }

        //return response
        return response()->json([
            'success' => true,
            'message' => 'User Berhasil Dikick',
        ]);
    }

    public function regist(Request $request)
    {
        // $user = PppoeUser::whereIn('id', $request->ids);
        // $user->update([
        //     'status' => 1,
        // ]);
        
        $notif_sm = BillingSetting::where('shortname', multi_auth()->shortname)->first()->notif_sm;
        if ($notif_sm === 1) {
            $mpwa = Mpwa::where('shortname', multi_auth()->shortname)->first();
            $users_pppoe = PppoeUser::whereIn('id', $request->ids)->with('rprofile')->get();
            foreach ($users_pppoe as $row) {
                if ($row->wa != null) {
                    $amount_ppn = ($row->rprofile->price * $row->ppn) / 100;
                    $amount_discount = $row->discount;
                    $total = $row->rprofile->price + $amount_ppn - $amount_discount;
                    $shortcode = ['[nama_lengkap]', '[id_pelanggan]', '[username]', '[password]', '[alamat]', '[paket_internet]', '[harga]', '[ppn]', '[discount]', '[total]', '[tipe_pembayaran]', '[siklus_tagihan]', '[tgl_aktif]', '[jth_tempo]'];
                    $source = [$row->full_name, $row->id_pelanggan, $row->username, $row->password, $row->address, $row->profile, number_format($row->rprofile->price, 0, '.', '.'), $row->ppn, number_format($row->discount, 0, '.', '.'), number_format($total, 0, '.', '.'), $row->payment_type, $row->billing_period, date('d/m/Y', strtotime($row->reg_date)), date('d/m/Y', strtotime($row->next_due))];
                    $template = Watemplate::where('shortname', $row->shortname)->first()->account_regist;
                    $message = str_replace($shortcode, $source, $template);
                    $message_format = str_replace('<br>', "\n", $message);

                    try {
                        $curl = curl_init();
                        $data = [
                            'api_key' => $mpwa->api_key,
                            'sender' => $mpwa->sender,
                            'number' => $row->wa,
                            'message' => $message_format,
                        ];
                        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
                        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
                        curl_setopt($curl, CURLOPT_URL, 'https://' . $mpwa->mpwa_server . '/send-message');
                        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
                        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
                        $response = curl_exec($curl);
                        curl_close($curl);
                        // $result = json_decode($response, true);
                    } catch (\Exception $e) {
                        return $e->getMessage();
                    }
                }
            }
        }

        $totaluser = PppoeUser::where('shortname', multi_auth()->shortname)->count();
        $totalactive = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 1)->count();
        $totalsuspend = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 2)->count();
        $totaldisabled = PppoeUser::where('shortname', multi_auth()->shortname)->where('status', 0)->count();

        //return response
        return response()->json([
            'success' => true,
            'message' => 'Progres Registrasi Berhasil',
            // 'data' => $user,
            'totaluser' => $totaluser,
            'totalactive' => $totalactive,
            'totalsuspend' => $totalsuspend,
            'totaldisabled' => $totaldisabled,
        ]);
    }

    public function edit(Request $request){
        

        if($request->profile_id == null || $request->profile_id == ''){
            $pppoe = PppoeUser::whereIn('id', $request->ids)->update([
                // 'profile' => $request->profile,
                // 'profile_id' => $request->profile_id,
                'nas' => $request->nas,
            ]);
        }else if($request->nas == null || $request->nas == ''){
            $pppoe = PppoeUser::whereIn('id', $request->ids)->update([
                'profile' => $request->profile,
                'profile_id' => $request->profile_id,
                // 'nas' => $request->nas,
            ]);
        }else{
            $pppoe = PppoeUser::whereIn('id', $request->ids)->update([
                'profile' => $request->profile,
                'profile_id' => $request->profile_id,
                'nas' => $request->nas,
            ]);
        }

        $totals = PppoeUser::where('shortname', multi_auth()->shortname)->selectRaw('count(*) as totaluser')->selectRaw('sum(status = 1) as totalactive')->selectRaw('sum(status = 2) as totalsuspend')->selectRaw('sum(status = 0) as totaldisabled')->first();

        return response()->json([
            'success' => true,
            'message' => 'User Berhasil Diupdate',
            'totaluser' => $totals->totaluser,
            'totalactive' => $totals->totalactive,
            'totalsuspend' => $totals->totalsuspend,
            'totaldisabled' => $totals->totaldisabled,
        ]);
    }

    public function delete(Request $request)
    {
        // SSH configuration
        // $sshUser = env('IP_RADIUS_USERNAME');
        // $sshHost = env('IP_RADIUS_SERVER');
        // $sshOptions = ['-o', 'BatchMode=yes', '-o', 'StrictHostKeyChecking=no'];
        // $sshOptionsString = implode(' ', $sshOptions);

        // // Fetch users to delete
        // $userList = PppoeUser::whereIn('id', $request->ids)->select('username', 'nas')->get();

        // // Preload default NAS entries for users without specific NAS
        // $defaultNasList = RadiusNas::where('shortname', multi_auth()->shortname)->get(['nasname', 'secret']);

        // foreach ($userList as $user) {
        //     $nasItems = $user->nas ? RadiusNas::where('nasname', $user->nas)->get(['nasname', 'secret']) : $defaultNasList;

        //     foreach ($nasItems as $nas) {
        //         $usernameArg = escapeshellarg("User-Name = '{$user->username}'");
        //         $radclientCmd = "echo {$usernameArg} | radclient -r 1 {$nas->nasname}:3799 disconnect {$nas->secret}";
        //         $sshCommand = "ssh {$sshOptionsString} {$sshUser}@{$sshHost} \"{$radclientCmd}\"";
        //         $process = Process::run($sshCommand);

        //         // Log::info('SSH delete disconnect executed', [
        //         //     'command' => $sshCommand,
        //         //     'output' => $process->output(),
        //         //     'success' => $process->successful(),
        //         // ]);

        //         if (!$process->successful()) {
        //             // Log::error('SSH delete disconnect failed', [
        //             //     'command' => $sshCommand,
        //             //     'error' => $process->errorOutput(),
        //             // ]);
        //         }
        //     }

        //     activity()
        //         ->tap(fn(Activity $activity) => ($activity->shortname = multi_auth()->shortname))
        //         ->event('Delete')
        //         ->log("Delete User PPPoE: {$user->username}");
        // }

        // Delete user records
        PppoeUser::whereIn('id', $request->ids)->delete();
        Invoice::whereIn('id_pelanggan', $request->ids)->delete();
        $totals = PppoeUser::where('shortname', multi_auth()->shortname)->selectRaw('count(*) as totaluser')->selectRaw('sum(status = 1) as totalactive')->selectRaw('sum(status = 2) as totalsuspend')->selectRaw('sum(status = 0) as totaldisabled')->first();

        return response()->json([
            'success' => true,
            'message' => 'User Berhasil Dihapus',
            'totaluser' => $totals->totaluser,
            'totalactive' => $totals->totalactive,
            'totalsuspend' => $totals->totalsuspend,
            'totaldisabled' => $totals->totaldisabled,
        ]);
    }

    public function online()
    {
        if (request()->ajax()) {
            $sub = RadiusSession::selectRaw('MAX(start) as latest_start, username')->where('shortname', multi_auth()->shortname)->groupBy('username');
            $online = RadiusSession::select(['user_session.session_id', 'user_session.username', 'user_session.ip', 'user_session.mac', 'user_session.input', 'user_session.output', 'user_session.uptime', 'user_session.start', 'user_session.stop', 'user_session.nas_address'])
                ->joinSub($sub, 'latest', function ($join) {
                    $join->on('user_session.username', '=', 'latest.username')->on('user_session.start', '=', 'latest.latest_start');
                })
                ->where([
                    ['user_session.shortname', '=', multi_auth()->shortname],
                    ['user_session.status', '=', 1],
                    ['user_session.type', '=', 2],
                    ['user_session.stop', '=', null], // hanya yang belum stop
                ])
                ->with('mnas', 'ppp:username,full_name,kode_area,kode_odp')
                ->get();

            return DataTables::of($online)->addIndexColumn()->toJson();
        }
        return view('backend.pppoe.online.index');
    }

    public function offline()
    {
        if (request()->ajax()) {
            // Ambil session terakhir per user
            $sub = RadiusSession::selectRaw('MAX(start) as latest_start, username')->where('shortname', multi_auth()->shortname)->groupBy('username');

            // Join dengan session asli untuk ambil seluruh datanya
            $offline = RadiusSession::select(['user_session.session_id', 'user_session.username', 'user_session.ip', 'user_session.mac', 'user_session.input', 'user_session.output', 'user_session.nas_address', 'user_session.uptime', 'user_session.start', 'user_session.stop', 'user_session.status'])
                ->joinSub($sub, 'latest', function ($join) {
                    $join->on('user_session.username', '=', 'latest.username')->on('user_session.start', '=', 'latest.latest_start');
                })
                ->where('user_session.shortname', multi_auth()->shortname)
                ->where('user_session.type', 2)
                ->where('user_session.status', 2) // offline only
                ->with('mnas', 'ppp:username,full_name,kode_area,kode_odp')
                ->get();

            return DataTables::of($offline)->addIndexColumn()->toJson();
        }
        return view('backend.pppoe.offline.index');
    }
    public function sync()
    {
        $ssh_user = env('IP_RADIUS_USERNAME');
        $ssh_host = env('IP_RADIUS_SERVER');
        $sshOptions = ['-o', 'BatchMode=yes', '-o', 'StrictHostKeyChecking=no'];
        $sshOptionsString = implode(' ', $sshOptions);

        $user_nas = PppoeUser::where('shortname', multi_auth()->shortname)->select('username', 'nas')->get();
        foreach ($user_nas as $row) {
            if ($row->nas === null) {
                $nas = RadiusNas::where('shortname', multi_auth()->shortname)->select('nasname', 'secret')->get();
                foreach ($nas as $item) {
                    $username = escapeshellarg("User-Name = '{$row->username}'");
                    $command = "echo $username | radclient -r 1 {$item['nasname']}:3799 disconnect {$item['secret']}";
                    $ssh_command = "ssh {$sshOptionsString} {$ssh_user}@{$ssh_host} \"{$command}\"";
                    $process = Process::run($ssh_command);
                }
            } else {
                $secret = RadiusNas::where('nasname', $row->nas)->select('secret')->first();
                $username = escapeshellarg("User-Name = '{$row->username}'");
                $command = "echo $username | radclient -r 1 {$row->nas}:3799 disconnect {$secret->secret}";
                $ssh_command = "ssh {$sshOptionsString} {$ssh_user}@{$ssh_host} \"{$command}\"";
                $process = Process::run($ssh_command);
            }
            RadiusSession::where('shortname', multi_auth()->shortname)->where('username', $row->username)->delete();
        }
        activity()
            ->tap(function (Activity $activity) {
                $activity->shortname = multi_auth()->shortname;
            })
            ->event('Update')
            ->log('Resync All User PPPoE');

        return response()->json([
            'success' => true,
            'message' => 'User Berhasil Disinkronkan',
        ]);
    }

    public function syncOffline(Request $request)
    {
        $offline = RadiusSession::where('shortname', multi_auth()->shortname)->where('username', $request->username)->where('type', 2)->where('status', 2)->delete();
        // activity()
        //     ->tap(function (Activity $activity) {
        //         $activity->shortname = multi_auth()->shortname;
        //     })
        //     ->event('Update')
        //     ->log('Delete Session Offline');

        return response()->json([
            'success' => true,
            'message' => 'Sesi Berhasil Dihapus',
            'data' => $offline,
        ]);
    }

    public function clearSession(Request $request)
    {
        $session = RadiusSession::where('shortname', multi_auth()->shortname)->where('username', $request->username)->delete();
        return response()->json([
            'success' => true,
            'message' => 'Sesi Berhasil Dihapus',
            'data' => $session,
        ]);
    }

    public function export()
    {
        return Excel::download(new PppoeUserExport(), 'pppoe_users.xlsx');
    }

    public function getSession(Request $request)
    {
        $sessions = RadiusSession::where('shortname', multi_auth()->shortname)->where('username', $request->username)->orderBy('id', 'desc')->get();
        return response()->json($sessions);
    }

    public function import(Request $request)
    {
        // Validasi file import (sesuaikan ekstensi file yang diperbolehkan)
        $request->validate([
            'select_file' => 'required|mimes:xlsx,xls,csv',
        ]);

        try {
            Excel::import(new PppoeUserImport(), $request->file('select_file'));
            return redirect()->back()->with('success', 'Import user berhasil.');
        } catch (\Exception $e) {
            // Untuk debugging, Anda bisa menyimpan pesan error ke session dan menampilkannya di view
            return redirect()
                ->back()
                ->with('error', 'Import user gagal: ' . $e->getMessage());
        }
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Auth;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        // Misalnya, $roles adalah array role yang diizinkan
        $allowed = false;

        // Cek jika guard "user" telah terautentikasi
        if (Auth::guard('web')->check()) {
            $user = Auth::guard('web')->user();
            if (in_array($user->role, $roles)) {
                $allowed = true;
            }
        }

        // Cek jika guard "mitra" telah terautentikasi
        if (Auth::guard('mitra')->check()) {
            $mitra = Auth::guard('mitra')->user();
            if (in_array($mitra->role, $roles)) {
                $allowed = true;
            }
        }

        if (Auth::guard('reseller')->check()) {
            $reseller = Auth::guard('reseller')->user();
            if (in_array($reseller->role, $roles)) {
                $allowed = true;
            }
        }

        if ($allowed) {
            return $next($request);
        }
        // abort(401);

        return redirect('/account');
        // if ($request->user()->role !== $role) {
        //     abort(401);
        // }
        // return $next($request);
    }
}

<?php

use Illuminate\Support\Facades\Auth;

if (! function_exists('multi_auth')) {
    function multi_auth()
    {
        if (Auth::guard('web')->check()) {
            return Auth::guard('web')->user();
        }
        if (Auth::guard('mitra')->check()) {
            return Auth::guard('mitra')->user();
        }
        if (Auth::guard('reseller')->check()) {
            return Auth::guard('reseller')->user();
        }
        return null;        
    }
}


<?php

namespace App\Models\Setting;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Role extends Model
{
    use HasFactory;
    protected $connection = 'mysql';
    protected $table = 'setting_role';
    protected $fillable = [
        'shortname',
        'username',
        'crud_invoice',
        'pay_invoice',
        'ringkasan_invoice',
        'menu_keuangan',
        'ringkasan_keuangan',
        'crud_transaksi',
        'laporan_keuangan',
        'payment_gateway',
        'menu_radius',
        'create_user_hs',
        'create_user_pppoe',

    ];
}
